#!/usr/bin/env python3
"""
Quick Endpoint Test Script
=========================

This script tests all endpoints without requiring database setup.
It verifies that endpoints exist and respond correctly.
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from fastapi.testclient import TestClient
    from app.main import app
    
    def test_endpoints():
        """Test all endpoints quickly"""
        client = TestClient(app)
        results = {}
        
        print("🧪 Testing GrowthHive Endpoints")
        print("=" * 60)
        
        # Test basic endpoints
        tests = [
            ("GET", "/", "Root endpoint"),
            ("GET", "/api/", "API root"),
            ("GET", "/health", "Health check"),
            ("GET", "/api/docs", "Swagger docs"),
            ("GET", "/api/redoc", "ReDoc docs"),
            ("GET", "/api/openapi.json", "OpenAPI schema"),
            
            # Auth endpoints
            ("POST", "/api/auth/register", "Register endpoint", {}),
            ("POST", "/api/auth/login", "Login endpoint", {}),
            
            # Category endpoints
            ("GET", "/api/categories", "List categories"),
            ("POST", "/api/categories", "Create category", {}),
            
            # Subcategory endpoints
            ("GET", "/api/categories/test-id/subcategories", "List subcategories"),
            ("POST", "/api/categories/test-id/subcategories", "Create subcategory", {}),
            
            # Franchisor endpoints
            ("GET", "/api/franchisors", "List franchisors"),
            ("POST", "/api/franchisors", "Create franchisor", {}),
        ]
        
        passed = 0
        failed = 0
        
        for test in tests:
            method = test[0]
            url = test[1]
            description = test[2]
            data = test[3] if len(test) > 3 else None
            
            try:
                if method == "GET":
                    response = client.get(url)
                elif method == "POST":
                    response = client.post(url, json=data)
                else:
                    response = client.request(method, url, json=data)
                
                # Check if endpoint exists (not 404)
                if response.status_code == 404:
                    status = "❌ NOT FOUND"
                    failed += 1
                elif response.status_code in [422, 401]:
                    # These are expected for endpoints that require auth or validation
                    status = "✅ EXISTS (validation/auth required)"
                    passed += 1
                elif response.status_code in [200, 201]:
                    status = "✅ WORKING"
                    passed += 1
                else:
                    status = f"⚠️  STATUS {response.status_code}"
                    passed += 1  # Still counts as existing
                
                print(f"{method:4} {url:40} | {status}")
                results[f"{method} {url}"] = {
                    "status_code": response.status_code,
                    "working": response.status_code != 404
                }
                
            except Exception as e:
                print(f"{method:4} {url:40} | ❌ ERROR: {str(e)}")
                results[f"{method} {url}"] = {
                    "status_code": None,
                    "working": False,
                    "error": str(e)
                }
                failed += 1
        
        print("=" * 60)
        print(f"Results: {passed} working, {failed} failed")
        
        # Test OpenAPI schema structure
        try:
            response = client.get("/api/openapi.json")
            if response.status_code == 200:
                schema = response.json()
                paths = schema.get("paths", {})
                
                print(f"\nEndpoint Summary:")
                print(f"Total endpoints: {len(paths)}")
                
                # Count by category
                auth_endpoints = [p for p in paths if "/auth/" in p]
                category_endpoints = [p for p in paths if "/categories" in p]
                franchisor_endpoints = [p for p in paths if "/franchisors" in p]
                
                print(f"Auth endpoints: {len(auth_endpoints)}")
                print(f"Category endpoints: {len(category_endpoints)}")
                print(f"Franchisor endpoints: {len(franchisor_endpoints)}")
                
                # Show all endpoints
                print(f"\nAll Available Endpoints:")
                for path, methods in sorted(paths.items()):
                    method_list = ", ".join(methods.keys()).upper()
                    print(f"  {path}: {method_list}")
                
        except Exception as e:
            print(f"Error getting endpoint summary: {e}")
        
        return results, passed, failed
    
    def main():
        """Main function"""
        try:
            results, passed, failed = test_endpoints()
            
            print("\n" + "=" * 60)
            if failed == 0:
                print("🎉 ALL ENDPOINTS ARE WORKING!")
                print("✅ The API is properly configured and accessible.")
            else:
                print(f"⚠️  {failed} endpoints have issues, but {passed} are working.")
                print("🔧 Some endpoints may require database setup or authentication.")
            
            print("\nNext steps:")
            print("1. Set up database connection for full functionality")
            print("2. Configure authentication for protected endpoints")
            print("3. Run full test suite with: python test_runner.py")
            
            return 0 if failed == 0 else 1
            
        except Exception as e:
            print(f"❌ Error testing endpoints: {e}")
            print("\nTroubleshooting:")
            print("1. Ensure all dependencies are installed: pip install -r requirements.txt")
            print("2. Check that the app starts correctly: python -m uvicorn app.main:app")
            return 1
    
    if __name__ == "__main__":
        sys.exit(main())

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("\nTroubleshooting:")
    print("1. Ensure you're in the project root directory")
    print("2. Install dependencies: pip install -r requirements.txt")
    print("3. Check Python path and module structure")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    sys.exit(1)
