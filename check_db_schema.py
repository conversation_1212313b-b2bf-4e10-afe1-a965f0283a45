#!/usr/bin/env python3
"""
<PERSON>ript to check the current database schema
"""
import asyncio
import asyncpg
from app.core.config.settings import settings

async def check_database_schema():
    """Check the current database schema"""
    try:
        # Parse the database URL
        db_url = settings.DATABASE_URL
        # Convert asyncpg URL to regular postgres URL for asyncpg
        db_url = db_url.replace("postgresql+asyncpg://", "postgresql://")
        
        print(f"Connecting to database...")
        conn = await asyncpg.connect(db_url)
        
        # Check if users table exists and get its structure
        print("\n=== USERS TABLE STRUCTURE ===")
        result = await conn.fetch("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'users' 
            ORDER BY ordinal_position;
        """)
        
        if result:
            print("Users table columns:")
            for row in result:
                print(f"  {row['column_name']}: {row['data_type']} (nullable: {row['is_nullable']}) default: {row['column_default']}")
        else:
            print("Users table not found!")
        
        # Check if is_deleted column exists
        print(f"\n=== CHECKING is_deleted COLUMN ===")
        is_deleted_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'users' AND column_name = 'is_deleted'
            );
        """)
        print(f"is_deleted column exists: {is_deleted_exists}")
        
        # Check role column type
        print(f"\n=== CHECKING role COLUMN TYPE ===")
        role_info = await conn.fetchrow("""
            SELECT data_type, udt_name 
            FROM information_schema.columns 
            WHERE table_name = 'users' AND column_name = 'role';
        """)
        if role_info:
            print(f"Role column type: {role_info['data_type']} (UDT: {role_info['udt_name']})")
        else:
            print("Role column not found!")
        
        # Check if user_role enum exists
        print(f"\n=== CHECKING user_role ENUM ===")
        enum_values = await conn.fetch("""
            SELECT enumlabel 
            FROM pg_enum 
            WHERE enumtypid = (
                SELECT oid FROM pg_type WHERE typname = 'user_role'
            );
        """)
        if enum_values:
            print("user_role enum values:")
            for row in enum_values:
                print(f"  - {row['enumlabel']}")
        else:
            print("user_role enum not found!")
        
        # Check alembic version
        print(f"\n=== ALEMBIC VERSION ===")
        try:
            alembic_version = await conn.fetchval("SELECT version_num FROM alembic_version;")
            print(f"Current Alembic version: {alembic_version}")
        except Exception as e:
            print(f"Error getting Alembic version: {e}")
        
        await conn.close()
        print("\n=== DONE ===")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(check_database_schema())
