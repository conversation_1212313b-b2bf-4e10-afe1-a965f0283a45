#!/usr/bin/env python3
"""
Create a test user for API testing
"""

import asyncio
import uuid
from datetime import datetime
from app.core.database.connection import async_engine
from app.models.user import User
from app.core.security.password import get_password_hash
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import sessionmaker

async def create_test_user():
    """Create a test user"""
    
    # Create session
    AsyncSessionLocal = sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False
    )
    
    async with AsyncSessionLocal() as session:
        try:
            # Check if user already exists
            from sqlalchemy import select
            result = await session.execute(
                select(User).where(User.email == "<EMAIL>")
            )
            existing_user = result.scalar_one_or_none()
            
            if existing_user:
                print(f"User already exists: {existing_user.email}")
                print(f"User ID: {existing_user.id}")
                print(f"Is Active: {existing_user.is_active}")
                return existing_user
            
            # Create new user
            user_id = uuid.UUID("d8bed830-f9e8-48d0-b97c-c32aaf5a6457")  # Use the ID from the JWT
            password_hash = get_password_hash("testpassword")
            
            new_user = User(
                id=user_id,
                email="<EMAIL>",
                mobile="+2154635896",
                password_hash=password_hash,
                first_name="John",
                last_name="Doe",
                role="ADMIN",
                is_active=True,
                is_email_verified=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            session.add(new_user)
            await session.commit()
            await session.refresh(new_user)
            
            print(f"✅ Test user created successfully!")
            print(f"Email: {new_user.email}")
            print(f"ID: {new_user.id}")
            print(f"Role: {new_user.role}")
            print(f"Is Active: {new_user.is_active}")
            
            return new_user
            
        except Exception as e:
            await session.rollback()
            print(f"❌ Error creating user: {e}")
            raise

if __name__ == "__main__":
    asyncio.run(create_test_user())
