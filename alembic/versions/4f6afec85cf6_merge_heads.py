"""Me<PERSON> heads

Revision ID: 4f6afec85cf6
Revises: 002_add_holiday_and_messaging_rule_tables, 11747409b3ae
Create Date: 2025-06-26 16:34:59.040941

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '4f6afec85cf6'
down_revision: Union[str, None] = ('002_holiday_messaging', '11747409b3ae')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
