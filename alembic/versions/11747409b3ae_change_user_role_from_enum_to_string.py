"""Change user role from enum to string

Revision ID: 11747409b3ae
Revises: 46c979418266
Create Date: 2025-06-26 11:03:55.416434

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '11747409b3ae'
down_revision: Union[str, None] = '46c979418266'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('franchisors', sa.Column('category_id', sa.UUID(), nullable=True))
    op.add_column('franchisors', sa.Column('subcategory_id', sa.UUID(), nullable=True))
    op.drop_index('ix_franchisors_category', table_name='franchisors')
    op.drop_index('ix_franchisors_sub_category', table_name='franchisors')
    op.create_index(op.f('ix_franchisors_category_id'), 'franchisors', ['category_id'], unique=False)
    op.create_index(op.f('ix_franchisors_subcategory_id'), 'franchisors', ['subcategory_id'], unique=False)
    op.create_foreign_key(None, 'franchisors', 'category', ['category_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key(None, 'franchisors', 'subcategory', ['subcategory_id'], ['id'], ondelete='SET NULL')
    op.drop_column('franchisors', 'sub_category')
    op.drop_column('franchisors', 'category')
    op.alter_column('users', 'role',
               existing_type=postgresql.ENUM('ADMIN', 'USER', 'SUPERADMIN', name='user_role'),
               type_=sa.String(length=50),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'role',
               existing_type=sa.String(length=50),
               type_=postgresql.ENUM('ADMIN', 'USER', 'SUPERADMIN', name='user_role'),
               existing_nullable=False)
    op.add_column('franchisors', sa.Column('category', sa.VARCHAR(length=100), autoincrement=False, nullable=False))
    op.add_column('franchisors', sa.Column('sub_category', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'franchisors', type_='foreignkey')
    op.drop_constraint(None, 'franchisors', type_='foreignkey')
    op.drop_index(op.f('ix_franchisors_subcategory_id'), table_name='franchisors')
    op.drop_index(op.f('ix_franchisors_category_id'), table_name='franchisors')
    op.create_index('ix_franchisors_sub_category', 'franchisors', ['sub_category'], unique=False)
    op.create_index('ix_franchisors_category', 'franchisors', ['category'], unique=False)
    op.drop_column('franchisors', 'subcategory_id')
    op.drop_column('franchisors', 'category_id')
    # ### end Alembic commands ###
