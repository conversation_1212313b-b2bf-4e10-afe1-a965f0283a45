---
description: 
globs: 
alwaysApply: false
---
{
  "requireTests": true,
  "preferredTestingFramework": "pytest",
  "testingStrategy": "TDD",
  "allowUnimplementedTests": false,
  "autoRunTests": true,
  "yoloMode": true,
  "testCoverageEnforced": true,
  "minTestCoverage": 80,
  "preferredHTTPClient": "httpx",
  "preferAsyncTests": true,
  "requireDocstrings": true,
  "codeStyle": "black",
  "linting": {
    "enabled": true,
    "tools": ["ruff", "mypy"],
    "failOnLintError": true
  },
  "functionNamingConvention": "snake_case",
  "classNamingConvention": "PascalCase",
  "fileNamingConvention": "snake_case",
  "enforceLogging": true,
  "logFormat": "[%(asctime)s] %(levelname)s in %(module)s: %(message)s",
  "commentStyle": "Clear, concise, helpful",
  "preferPydanticV2": true,
  "envAware": true,
  "secureByDefault": true
}