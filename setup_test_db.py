#!/usr/bin/env python3
"""
Test Database Setup Script
==========================

This script sets up a test database for running tests.
It creates the database if it doesn't exist and runs migrations.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
from app.core.database.connection import Base
from app.models import *  # Import all models


async def create_test_database():
    """Create test database if it doesn't exist"""
    
    # Database connection details
    DB_HOST = os.getenv("DB_HOST", "localhost")
    DB_PORT = os.getenv("DB_PORT", "5432")
    DB_USER = os.getenv("DB_USER", "postgres")
    DB_PASSWORD = os.getenv("DB_PASSWORD", "password")
    DB_NAME = "growthhive_test"
    
    # Connect to postgres database to create test database
    postgres_url = f"postgresql+asyncpg://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/postgres"
    
    try:
        # Create engine for postgres database
        postgres_engine = create_async_engine(postgres_url, isolation_level="AUTOCOMMIT")
        
        async with postgres_engine.begin() as conn:
            # Check if test database exists
            result = await conn.execute(
                text("SELECT 1 FROM pg_database WHERE datname = :db_name"),
                {"db_name": DB_NAME}
            )
            
            if not result.fetchone():
                print(f"Creating test database: {DB_NAME}")
                await conn.execute(text(f"CREATE DATABASE {DB_NAME}"))
                print("✅ Test database created successfully")
            else:
                print(f"✅ Test database {DB_NAME} already exists")
        
        await postgres_engine.dispose()
        
        # Now connect to test database and create tables
        test_db_url = f"postgresql+asyncpg://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
        test_engine = create_async_engine(test_db_url)
        
        async with test_engine.begin() as conn:
            print("Creating database tables...")
            await conn.run_sync(Base.metadata.create_all)
            print("✅ Database tables created successfully")
        
        await test_engine.dispose()
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting up test database: {e}")
        print("\nTroubleshooting tips:")
        print("1. Ensure PostgreSQL is running")
        print("2. Check database credentials")
        print("3. Ensure the user has permission to create databases")
        print(f"4. Try connecting manually: psql -h {DB_HOST} -p {DB_PORT} -U {DB_USER}")
        return False


async def reset_test_database():
    """Reset test database by dropping and recreating all tables"""
    
    DB_HOST = os.getenv("DB_HOST", "localhost")
    DB_PORT = os.getenv("DB_PORT", "5432")
    DB_USER = os.getenv("DB_USER", "postgres")
    DB_PASSWORD = os.getenv("DB_PASSWORD", "password")
    DB_NAME = "growthhive_test"
    
    test_db_url = f"postgresql+asyncpg://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
    
    try:
        test_engine = create_async_engine(test_db_url)
        
        async with test_engine.begin() as conn:
            print("Dropping all tables...")
            await conn.run_sync(Base.metadata.drop_all)
            print("Creating all tables...")
            await conn.run_sync(Base.metadata.create_all)
            print("✅ Test database reset successfully")
        
        await test_engine.dispose()
        return True
        
    except Exception as e:
        print(f"❌ Error resetting test database: {e}")
        return False


def check_database_connection():
    """Check if we can connect to PostgreSQL"""
    import subprocess
    
    DB_HOST = os.getenv("DB_HOST", "localhost")
    DB_PORT = os.getenv("DB_PORT", "5432")
    DB_USER = os.getenv("DB_USER", "postgres")
    
    try:
        # Try to connect using psql
        result = subprocess.run([
            "psql", 
            f"-h{DB_HOST}", 
            f"-p{DB_PORT}", 
            f"-U{DB_USER}", 
            "-c", "SELECT 1;", 
            "postgres"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ PostgreSQL connection successful")
            return True
        else:
            print(f"❌ PostgreSQL connection failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ PostgreSQL connection timed out")
        return False
    except FileNotFoundError:
        print("❌ psql command not found. Please install PostgreSQL client tools.")
        return False
    except Exception as e:
        print(f"❌ Error checking PostgreSQL connection: {e}")
        return False


async def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Database Setup")
    parser.add_argument("--reset", action="store_true", help="Reset the test database")
    parser.add_argument("--check", action="store_true", help="Check database connection only")
    
    args = parser.parse_args()
    
    print("🗄️  GrowthHive Test Database Setup")
    print("=" * 50)
    
    if args.check:
        print("Checking database connection...")
        success = check_database_connection()
        sys.exit(0 if success else 1)
    
    if args.reset:
        print("Resetting test database...")
        success = await reset_test_database()
        sys.exit(0 if success else 1)
    
    # Default: create test database
    print("Setting up test database...")
    
    # First check if we can connect to PostgreSQL
    if not check_database_connection():
        print("\n❌ Cannot connect to PostgreSQL. Please check your setup.")
        sys.exit(1)
    
    # Create test database and tables
    success = await create_test_database()
    
    if success:
        print("\n✅ Test database setup completed successfully!")
        print("\nYou can now run tests with:")
        print("  python test_runner.py")
        print("  python -m pytest tests/")
    else:
        print("\n❌ Test database setup failed!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
