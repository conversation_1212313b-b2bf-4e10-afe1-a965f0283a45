"""
<PERSON><PERSON><PERSON> to create all tables for GrowthHive application.
Run this script whenever you need to (re)create all tables in the database.
"""
import asyncio
from app.core.database.connection import async_engine, DATABASE_URL
from sqlalchemy.ext.asyncio import AsyncEngine
from app.models import user, session, franchisor, lead, document, system_setting
from app.models import category, subcategory

async def create_all_tables(engine: AsyncEngine):
    async with engine.begin() as conn:
        print("[INFO] Dropping all tables (if exist)...")
        await conn.run_sync(user.Base.metadata.drop_all)
        print("[INFO] Creating all tables...")
        await conn.run_sync(user.Base.metadata.create_all)
        print("[INFO] All tables created successfully.")

if __name__ == "__main__":
    print(f"[DEBUG] Connecting to DB: {DATABASE_URL}")
    asyncio.run(create_all_tables(async_engine)) 