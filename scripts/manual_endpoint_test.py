import httpx
import sys

BASE_URL = "http://localhost:8000/api"
EMAIL = "<EMAIL>"
PASSWORD = "Admin@1234"


def print_section(title):
    print("\n" + "=" * 60)
    print(title)
    print("=" * 60)

def get_token():
    print_section("AUTHENTICATION")
    resp = httpx.post(f"{BASE_URL}/auth/login", json={
        "email_or_mobile": EMAIL,
        "password": PASSWORD,
        "remember_me": True
    })
    print("Status:", resp.status_code)
    print("Response:", resp.json())
    if resp.status_code != 200 or not resp.json().get("data"):
        print("Failed to authenticate.")
        sys.exit(1)
    return resp.json()["data"]["details"]["access_token"]

def create_category(token):
    print_section("CREATE CATEGORY")
    payload = {
        "name": "Manual Test Category",
        "description": "Category for manual subcategory test"
    }
    resp = httpx.post(f"{BASE_URL}/categories", json=payload, headers={"Authorization": f"Bearer {token}"})
    print("Status:", resp.status_code)
    print("Response:", resp.json())
    if resp.status_code != 201 or not resp.json().get("data"):
        print("Failed to create category.")
        sys.exit(1)
    return resp.json()["data"]["id"]

def create_subcategory(token, category_id):
    print_section("CREATE SUBCATEGORY")
    payload = {
        "name": "Manual Test Subcategory",
        "description": "Subcategory for manual test"
    }
    resp = httpx.post(f"{BASE_URL}/categories/{category_id}/subcategories", json=payload, headers={"Authorization": f"Bearer {token}"})
    print("Status:", resp.status_code)
    print("Response:", resp.json())
    if resp.status_code != 201 or not resp.json().get("data"):
        print("Failed to create subcategory.")
        sys.exit(1)
    return resp.json()["data"]["id"]

def list_categories(token):
    print_section("LIST CATEGORIES")
    resp = httpx.get(f"{BASE_URL}/categories", headers={"Authorization": f"Bearer {token}"})
    print("Status:", resp.status_code)
    print("Response:", resp.json())

def get_category(token, category_id):
    print_section("GET CATEGORY BY ID")
    resp = httpx.get(f"{BASE_URL}/categories/{category_id}", headers={"Authorization": f"Bearer {token}"})
    print("Status:", resp.status_code)
    print("Response:", resp.json())

def update_category(token, category_id):
    print_section("UPDATE CATEGORY")
    payload = {
        "name": "Manual Test Category Updated",
        "description": "Updated description"
    }
    resp = httpx.put(f"{BASE_URL}/categories/{category_id}", json=payload, headers={"Authorization": f"Bearer {token}"})
    print("Status:", resp.status_code)
    print("Response:", resp.json())

def list_subcategories_by_category(token, category_id):
    print_section("LIST SUBCATEGORIES BY CATEGORY")
    resp = httpx.get(f"{BASE_URL}/categories/{category_id}/subcategories", headers={"Authorization": f"Bearer {token}"})
    print("Status:", resp.status_code)
    print("Response:", resp.json())

def get_subcategory(token, subcategory_id):
    print_section("GET SUBCATEGORY BY ID")
    resp = httpx.get(f"{BASE_URL}/subcategories/{subcategory_id}", headers={"Authorization": f"Bearer {token}"})
    print("Status:", resp.status_code)
    print("Response:", resp.json())

def search_subcategories(token):
    print_section("SEARCH SUBCATEGORIES (ALL)")
    resp = httpx.get(f"{BASE_URL}/subcategories", headers={"Authorization": f"Bearer {token}"})
    print("Status:", resp.status_code)
    print("Response:", resp.json())

def update_subcategory(token, subcategory_id, category_id):
    print_section("UPDATE SUBCATEGORY")
    payload = {
        "name": "Manual Test Subcategory Updated",
        "description": "Updated subcategory description"
    }
    resp = httpx.put(f"{BASE_URL}/subcategories/{subcategory_id}", json=payload, headers={"Authorization": f"Bearer {token}"})
    print("Status:", resp.status_code)
    print("Response:", resp.json())

def main():
    token = get_token()
    category_id = create_category(token)
    subcategory_id = create_subcategory(token, category_id)
    list_categories(token)
    get_category(token, category_id)
    update_category(token, category_id)
    list_subcategories_by_category(token, category_id)
    get_subcategory(token, subcategory_id)
    search_subcategories(token)
    update_subcategory(token, subcategory_id, category_id)
    print("\nAll endpoint tests completed.")

if __name__ == "__main__":
    main() 