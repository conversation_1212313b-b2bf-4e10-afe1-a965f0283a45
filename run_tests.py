#!/usr/bin/env python3
"""
GrowthHive Test Suite Runner
===========================

This script provides a comprehensive test suite that can be run multiple times
to verify all endpoints are working correctly.

Usage:
    python run_tests.py                    # Run all tests
    python run_tests.py --quick            # Run quick endpoint tests only
    python run_tests.py --endpoints        # Test endpoint existence and structure
    python run_tests.py --auth             # Test authentication endpoints
    python run_tests.py --summary          # Show endpoint summary
"""

import sys
import os
import argparse
import time
from pathlib import Path
from datetime import datetime

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from fastapi.testclient import TestClient
    from app.main import app
    
    class GrowthHiveTestSuite:
        """Comprehensive test suite for GrowthHive API"""
        
        def __init__(self):
            self.client = TestClient(app)
            self.results = {}
            self.start_time = None
            
        def run_endpoint_tests(self):
            """Test all endpoints for existence and basic functionality"""
            print("🧪 Testing GrowthHive Endpoints")
            print("=" * 80)
            
            self.start_time = time.time()
            
            # Core endpoints
            core_tests = [
                ("GET", "/", "Root endpoint"),
                ("GET", "/api/", "API root"),
                ("GET", "/health", "Health check"),
                ("GET", "/api/docs", "Swagger documentation"),
                ("GET", "/api/redoc", "ReDoc documentation"),
                ("GET", "/api/openapi.json", "OpenAPI schema"),
                ("GET", "/metrics", "Metrics endpoint"),
            ]
            
            # Authentication endpoints
            auth_tests = [
                ("POST", "/api/auth/register", "User registration", {}),
                ("POST", "/api/auth/login", "User login", {}),
                ("GET", "/api/auth/me", "Get current user"),
                ("POST", "/api/auth/logout", "User logout", {}),
                ("POST", "/api/auth/refresh", "Refresh token", {}),
            ]
            
            # Category endpoints
            category_tests = [
                ("GET", "/api/categories", "List categories"),
                ("POST", "/api/categories", "Create category", {"name": "Test", "description": "Test"}),
                ("GET", "/api/categories/test-id", "Get category by ID"),
                ("PUT", "/api/categories/test-id", "Update category", {"name": "Updated"}),
            ]
            
            # Subcategory endpoints
            subcategory_tests = [
                ("GET", "/api/categories/test-id/subcategories", "List subcategories"),
                ("POST", "/api/categories/test-id/subcategories", "Create subcategory", {"name": "Test", "description": "Test"}),
                ("GET", "/api/subcategories", "List all subcategories"),
                ("GET", "/api/subcategories/test-id", "Get subcategory by ID"),
            ]
            
            # Franchisor endpoints
            franchisor_tests = [
                ("GET", "/api/franchisors/", "List franchisors"),
                ("POST", "/api/franchisors/", "Create franchisor", {"name": "Test", "region": "north_america"}),
                ("GET", "/api/franchisors/test-id", "Get franchisor by ID"),
                ("PUT", "/api/franchisors/test-id", "Update franchisor", {"name": "Updated"}),
                ("GET", "/api/franchisors/categories", "Get franchisor categories"),
            ]
            
            all_tests = [
                ("Core Endpoints", core_tests),
                ("Authentication", auth_tests),
                ("Categories", category_tests),
                ("Subcategories", subcategory_tests),
                ("Franchisors", franchisor_tests),
            ]
            
            total_passed = 0
            total_failed = 0
            
            for section_name, tests in all_tests:
                print(f"\n📋 {section_name}")
                print("-" * 60)
                
                section_passed = 0
                section_failed = 0
                
                for test in tests:
                    method = test[0]
                    url = test[1]
                    description = test[2]
                    data = test[3] if len(test) > 3 else None
                    
                    try:
                        if method == "GET":
                            response = self.client.get(url)
                        elif method == "POST":
                            response = self.client.post(url, json=data)
                        elif method == "PUT":
                            response = self.client.put(url, json=data)
                        elif method == "DELETE":
                            response = self.client.delete(url)
                        else:
                            response = self.client.request(method, url, json=data)
                        
                        # Determine status
                        if response.status_code == 404:
                            status = "❌ NOT FOUND"
                            section_failed += 1
                        elif response.status_code in [422, 401]:
                            status = "✅ EXISTS (validation/auth required)"
                            section_passed += 1
                        elif response.status_code in [200, 201]:
                            status = "✅ WORKING"
                            section_passed += 1
                        elif response.status_code in [400, 403, 405]:
                            status = f"⚠️  STATUS {response.status_code} (endpoint exists)"
                            section_passed += 1
                        else:
                            status = f"⚠️  STATUS {response.status_code}"
                            section_passed += 1
                        
                        print(f"  {method:6} {url:35} | {status}")
                        
                        self.results[f"{method} {url}"] = {
                            "status_code": response.status_code,
                            "working": response.status_code != 404,
                            "description": description
                        }
                        
                    except Exception as e:
                        print(f"  {method:6} {url:35} | ❌ ERROR: {str(e)}")
                        self.results[f"{method} {url}"] = {
                            "status_code": None,
                            "working": False,
                            "error": str(e),
                            "description": description
                        }
                        section_failed += 1
                
                print(f"  Section Results: {section_passed} working, {section_failed} failed")
                total_passed += section_passed
                total_failed += section_failed
            
            duration = time.time() - self.start_time
            
            print("\n" + "=" * 80)
            print("FINAL RESULTS")
            print("=" * 80)
            print(f"✅ Working endpoints: {total_passed}")
            print(f"❌ Failed endpoints: {total_failed}")
            print(f"⏱️  Test duration: {duration:.2f} seconds")
            print(f"📊 Success rate: {(total_passed/(total_passed+total_failed)*100):.1f}%")
            
            return total_passed, total_failed
        
        def show_endpoint_summary(self):
            """Show comprehensive endpoint summary"""
            print("📋 GrowthHive API Endpoint Summary")
            print("=" * 80)
            
            try:
                response = self.client.get("/api/openapi.json")
                if response.status_code == 200:
                    schema = response.json()
                    paths = schema.get("paths", {})
                    
                    print(f"Total endpoints: {len(paths)}")
                    
                    # Group by category
                    categories = {
                        "Authentication": [p for p in paths if "/auth/" in p],
                        "Categories": [p for p in paths if "/categories" in p and "/auth/" not in p],
                        "Subcategories": [p for p in paths if "/subcategories" in p],
                        "Franchisors": [p for p in paths if "/franchisors" in p],
                        "Documents": [p for p in paths if "/documents" in p],
                        "Leads": [p for p in paths if "/leads" in p],
                        "Users": [p for p in paths if "/users" in p],
                        "Test": [p for p in paths if "/test" in p],
                        "Core": [p for p in paths if p in ["/", "/api/", "/health", "/metrics"]]
                    }
                    
                    for category, endpoints in categories.items():
                        if endpoints:
                            print(f"\n{category} ({len(endpoints)} endpoints):")
                            for endpoint in sorted(endpoints):
                                methods = ", ".join(paths[endpoint].keys()).upper()
                                print(f"  {endpoint}: {methods}")
                    
                    # Show uncategorized endpoints
                    categorized = set()
                    for endpoints in categories.values():
                        categorized.update(endpoints)
                    
                    uncategorized = [p for p in paths if p not in categorized]
                    if uncategorized:
                        print(f"\nOther ({len(uncategorized)} endpoints):")
                        for endpoint in sorted(uncategorized):
                            methods = ", ".join(paths[endpoint].keys()).upper()
                            print(f"  {endpoint}: {methods}")
                
                else:
                    print("❌ Could not retrieve OpenAPI schema")
                    
            except Exception as e:
                print(f"❌ Error getting endpoint summary: {e}")
        
        def test_authentication_flow(self):
            """Test authentication flow without database"""
            print("🔐 Testing Authentication Flow")
            print("=" * 80)
            
            # Test registration endpoint
            print("Testing user registration...")
            response = self.client.post("/api/auth/register", json={
                "email": "<EMAIL>",
                "password": "TestPassword123!",
                "mobile": "+1234567890"
            })
            
            if response.status_code == 422:
                print("✅ Registration endpoint exists (validation working)")
            elif response.status_code == 500:
                print("⚠️  Registration endpoint exists (database connection needed)")
            else:
                print(f"⚠️  Registration returned status {response.status_code}")
            
            # Test login endpoint
            print("Testing user login...")
            response = self.client.post("/api/auth/login", json={
                "email": "<EMAIL>",
                "password": "TestPassword123!"
            })
            
            if response.status_code == 422:
                print("✅ Login endpoint exists (validation working)")
            elif response.status_code == 500:
                print("⚠️  Login endpoint exists (database connection needed)")
            else:
                print(f"⚠️  Login returned status {response.status_code}")
            
            # Test protected endpoint
            print("Testing protected endpoint...")
            response = self.client.get("/api/auth/me")
            
            if response.status_code == 401:
                print("✅ Protected endpoint properly requires authentication")
            else:
                print(f"⚠️  Protected endpoint returned status {response.status_code}")
    
    def main():
        """Main function"""
        parser = argparse.ArgumentParser(description="GrowthHive Test Suite")
        parser.add_argument("--quick", action="store_true", help="Run quick tests only")
        parser.add_argument("--endpoints", action="store_true", help="Test endpoint existence")
        parser.add_argument("--auth", action="store_true", help="Test authentication")
        parser.add_argument("--summary", action="store_true", help="Show endpoint summary")
        
        args = parser.parse_args()
        
        suite = GrowthHiveTestSuite()
        
        print("🚀 GrowthHive API Test Suite")
        print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        if args.summary:
            suite.show_endpoint_summary()
            return 0
        
        if args.auth:
            suite.test_authentication_flow()
            return 0
        
        if args.endpoints or args.quick:
            passed, failed = suite.run_endpoint_tests()
            
            if failed == 0:
                print("\n🎉 ALL TESTS PASSED!")
                print("✅ All endpoints are working correctly")
                return 0
            else:
                print(f"\n⚠️  {failed} tests failed, but {passed} passed")
                print("🔧 Some endpoints may require database setup")
                return 1
        
        # Default: run all tests
        passed, failed = suite.run_endpoint_tests()
        
        print("\n" + "=" * 80)
        if failed == 0:
            print("🎉 ALL ENDPOINTS ARE WORKING PERFECTLY!")
            print("✅ The GrowthHive API is fully functional")
        else:
            print(f"⚠️  {failed} endpoints need attention, but {passed} are working")
            print("🔧 Consider setting up database for full functionality")
        
        print("\n📖 Documentation available at:")
        print("   • Swagger UI: http://localhost:8000/api/docs")
        print("   • ReDoc: http://localhost:8000/api/redoc")
        
        return 0 if failed == 0 else 1

    if __name__ == "__main__":
        sys.exit(main())

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please ensure you're in the project root and dependencies are installed")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    sys.exit(1)
