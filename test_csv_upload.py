#!/usr/bin/env python3
"""
Test script to verify CSV upload functionality with is_active and is_deleted fields
"""

import requests
import json
import io

def register_test_user():
    """Register a test user for authentication"""
    register_url = "http://127.0.0.1:8000/api/auth/register"

    user_data = {
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "confirm_password": "TestPassword123!",
        "first_name": "Test",
        "last_name": "User",
        "mobile": "+1234567890",
        "role": "admin"
    }

    try:
        response = requests.post(register_url, json=user_data)
        if response.status_code in [200, 201]:
            print(f"✅ Test user registered successfully")
            return True
        else:
            print(f"⚠️ User registration: {response.status_code} - {response.text}")
            return True  # User might already exist
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return False

def login_and_get_token():
    """Login and get authentication token"""
    login_url = "http://127.0.0.1:8000/api/auth/login"

    # Try to login with test credentials
    login_data = {
        "email_or_mobile": "<EMAIL>",
        "password": "TestPassword123!",
        "remember_me": False
    }

    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            data = response.json()
            return data.get("data", {}).get("access_token")
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_csv_upload():
    """Test CSV upload with the new fields"""

    # First try to register a test user
    register_test_user()

    # Get authentication token
    token = login_and_get_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return
    
    print(f"✅ Successfully authenticated")
    
    # Create test CSV content with new fields
    csv_content = """full_name,contact_number,email,location,lead_source,franchise_preference,budget_preference,qualification_status,is_active,is_deleted
Test User 1,+**********,<EMAIL>,California,Website,Food,60000,new,true,false
Test User 2,+**********,<EMAIL>,Texas,Referral,Retail,80000,contacted,1,0
Test User 3,+**********,<EMAIL>,Florida,Social Media,Health,90000,qualified,yes,no
Test User 4,+**********,<EMAIL>,Nevada,Email,Tech,120000,new,TRUE,FALSE
Test User 5,+**********,<EMAIL>,Oregon,Phone,Food,95000,qualified,,
Invalid User,+**********,<EMAIL>,Utah,Website,Food,85000,new,invalid,false"""
    
    # Prepare the file upload
    files = {
        'file': ('test_leads.csv', io.BytesIO(csv_content.encode('utf-8')), 'text/csv')
    }
    
    headers = {
        'Authorization': f'Bearer {token}'
    }
    
    upload_url = "http://127.0.0.1:8000/api/leads/bulk-upload"
    
    try:
        print(f"📤 Uploading CSV with {len(csv_content.split(chr(10))) - 1} rows...")
        response = requests.post(upload_url, files=files, headers=headers)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Upload successful!")
            print(f"   Total processed: {data['data']['total_processed']}")
            print(f"   Successful imports: {data['data']['successful_imports']}")
            print(f"   Duplicates found: {data['data']['duplicates_found']}")
            print(f"   Errors found: {data['data']['errors_found']}")
            
            if data['data'].get('errors'):
                print(f"   Errors: {data['data']['errors']}")
            
            if data['data'].get('duplicates'):
                print(f"   Duplicates: {data['data']['duplicates']}")
                
        else:
            print(f"❌ Upload failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Upload error: {e}")

def test_lead_retrieval(token):
    """Test retrieving leads to verify the new fields are present"""
    
    headers = {
        'Authorization': f'Bearer {token}'
    }
    
    list_url = "http://127.0.0.1:8000/api/leads/"
    
    try:
        response = requests.get(list_url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            leads = data.get('data', {}).get('items', [])
            
            print(f"\n📋 Retrieved {len(leads)} leads:")
            for i, lead in enumerate(leads[:3], 1):  # Show first 3 leads
                print(f"   {i}. {lead['full_name']} - Active: {lead.get('is_active')}, Deleted: {lead.get('is_deleted')}")
                
        else:
            print(f"❌ Failed to retrieve leads: {response.text}")
            
    except Exception as e:
        print(f"❌ Retrieval error: {e}")

if __name__ == "__main__":
    print("🧪 Testing CSV Upload with is_active and is_deleted fields")
    print("=" * 60)
    
    # Test CSV upload
    test_csv_upload()
    
    # Get token again for lead retrieval test
    token = login_and_get_token()
    if token:
        test_lead_retrieval(token)
    
    print("\n✅ Test completed!")
