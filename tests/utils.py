"""
Test utilities for standardized testing patterns
"""

from typing import Dict, Any, Optional
from httpx import AsyncClient
from fastapi import status


class TestAssertions:
    """Standardized test assertions for consistent testing patterns"""
    
    @staticmethod
    def assert_success_response(
        response_data: Dict[str, Any], 
        expected_status: bool = True,
        expected_error_code: int = 0
    ):
        """Assert standard success response format"""
        assert response_data["success"] == expected_status
        assert response_data["error_code"] == expected_error_code
        assert "message" in response_data
        assert "title" in response_data["message"]
        assert "description" in response_data["message"]
    
    @staticmethod
    def assert_error_response(
        response_data: Dict[str, Any],
        expected_error_code: Optional[int] = None,
        expected_message_contains: Optional[str] = None
    ):
        """Assert standard error response format"""
        assert response_data["success"] is False
        if expected_error_code:
            assert response_data["error_code"] == expected_error_code
        assert "message" in response_data
        assert "title" in response_data["message"]
        assert "description" in response_data["message"]
        if expected_message_contains:
            assert expected_message_contains.lower() in response_data["message"]["description"].lower()
    
    @staticmethod
    def assert_pagination_response(response_data: Dict[str, Any]):
        """Assert pagination response format"""
        assert "data" in response_data
        assert isinstance(response_data["data"], list)
    
    @staticmethod
    def assert_uuid_format(uuid_string: str):
        """Assert UUID format"""
        import uuid
        try:
            uuid.UUID(uuid_string)
        except ValueError:
            assert False, f"Invalid UUID format: {uuid_string}"


class TestDataFactory:
    """Factory for creating test data"""
    
    @staticmethod
    def create_category_data(name: str = "Test Category", description: str = "Test description") -> Dict[str, Any]:
        """Create category test data"""
        return {
            "name": name,
            "description": description
        }
    
    @staticmethod
    def create_subcategory_data(name: str = "Test Subcategory", description: str = "Test description") -> Dict[str, Any]:
        """Create subcategory test data"""
        return {
            "name": name,
            "description": description
        }
    
    @staticmethod
    def create_user_data(
        email: str = "<EMAIL>",
        password: str = "TestPassword123!",
        mobile: str = "+1234567890"
    ) -> Dict[str, Any]:
        """Create user test data"""
        return {
            "email": email,
            "password": password,
            "mobile": mobile
        }
    
    @staticmethod
    def create_franchisor_data(
        name: str = "Test Franchisor",
        category_id: Optional[str] = None,
        subcategory_id: Optional[str] = None,
        region: str = "north_america",
        budget: float = 100000.0
    ) -> Dict[str, Any]:
        """Create franchisor test data"""
        return {
            "name": name,
            "category_id": category_id,
            "subcategory_id": subcategory_id,
            "region": region,
            "budget": budget,
            "is_active": True
        }


class TestHelpers:
    """Helper functions for common test operations"""
    
    @staticmethod
    async def create_test_category(client: AsyncClient, name: str = "Test Category") -> Dict[str, Any]:
        """Create a test category and return the response data"""
        payload = TestDataFactory.create_category_data(name=name)
        response = await client.post("/api/categories", json=payload)
        assert response.status_code == status.HTTP_201_CREATED
        return response.json()
    
    @staticmethod
    async def create_test_subcategory(
        client: AsyncClient, 
        category_id: str, 
        name: str = "Test Subcategory"
    ) -> Dict[str, Any]:
        """Create a test subcategory and return the response data"""
        payload = TestDataFactory.create_subcategory_data(name=name)
        response = await client.post(f"/api/categories/{category_id}/subcategories", json=payload)
        assert response.status_code == status.HTTP_201_CREATED
        return response.json()
    
    @staticmethod
    async def create_test_franchisor(
        client: AsyncClient,
        name: str = "Test Franchisor",
        category_id: Optional[str] = None,
        subcategory_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create a test franchisor and return the response data"""
        payload = TestDataFactory.create_franchisor_data(
            name=name,
            category_id=category_id,
            subcategory_id=subcategory_id
        )
        response = await client.post("/api/franchisors", json=payload)
        assert response.status_code == status.HTTP_201_CREATED
        return response.json()
    
    @staticmethod
    async def cleanup_test_data(client: AsyncClient, resource_type: str, resource_id: str):
        """Clean up test data after tests"""
        endpoints = {
            "category": f"/api/categories/{resource_id}",
            "subcategory": f"/api/subcategories/{resource_id}",
            "franchisor": f"/api/franchisors/{resource_id}"
        }
        
        if resource_type in endpoints:
            await client.delete(endpoints[resource_type])


# Common test constants
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "TestPassword123!"
TEST_USER_MOBILE = "+1234567890"

# Common HTTP status codes for testing
HTTP_STATUS_CODES = {
    "OK": status.HTTP_200_OK,
    "CREATED": status.HTTP_201_CREATED,
    "BAD_REQUEST": status.HTTP_400_BAD_REQUEST,
    "UNAUTHORIZED": status.HTTP_401_UNAUTHORIZED,
    "FORBIDDEN": status.HTTP_403_FORBIDDEN,
    "NOT_FOUND": status.HTTP_404_NOT_FOUND,
    "CONFLICT": status.HTTP_409_CONFLICT,
    "UNPROCESSABLE_ENTITY": status.HTTP_422_UNPROCESSABLE_ENTITY,
    "INTERNAL_SERVER_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR
}

# Common error codes for testing
ERROR_CODES = {
    "VALIDATION_ERROR": 4000,
    "AUTHENTICATION_ERROR": 4001,
    "AUTHORIZATION_ERROR": 4002,
    "NOT_FOUND_ERROR": 4003,
    "DUPLICATE_ENTRY": 4004,
    "INTERNAL_SERVER_ERROR": 5000
}
