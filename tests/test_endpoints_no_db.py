"""
Endpoint tests that don't require database connection.
These tests verify that endpoints exist and respond correctly without database operations.
"""

import pytest
from fastapi.testclient import TestClient
from app.main import app


class TestEndpointsWithoutDB:
    """Test endpoints without database dependency"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_root_endpoint(self):
        """Test API root endpoint"""
        response = self.client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
    
    def test_api_root_endpoint(self):
        """Test API v1 root endpoint"""
        response = self.client.get("/api/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
    
    def test_health_endpoint(self):
        """Test health check endpoint"""
        response = self.client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
    
    def test_docs_endpoints(self):
        """Test documentation endpoints"""
        # Test Swagger UI
        response = self.client.get("/docs")
        assert response.status_code == 200
        
        # Test ReDoc
        response = self.client.get("/redoc")
        assert response.status_code == 200
        
        # Test OpenAPI schema
        response = self.client.get("/openapi.json")
        assert response.status_code == 200
        schema = response.json()
        assert "openapi" in schema
        assert "paths" in schema
    
    def test_auth_endpoints_exist(self):
        """Test that auth endpoints exist (without database)"""
        # Test register endpoint exists
        response = self.client.post("/api/auth/register", json={})
        # Should return 422 (validation error) not 404 (not found)
        assert response.status_code == 422
        
        # Test login endpoint exists
        response = self.client.post("/api/auth/login", json={})
        # Should return 422 (validation error) not 404 (not found)
        assert response.status_code == 422
    
    def test_category_endpoints_exist(self):
        """Test that category endpoints exist"""
        # Test list categories
        response = self.client.get("/api/categories")
        # Should not return 404 (endpoint should exist)
        assert response.status_code != 404
        
        # Test create category
        response = self.client.post("/api/categories", json={})
        # Should return 422 (validation error) or 401 (auth required), not 404
        assert response.status_code in [422, 401]
    
    def test_subcategory_endpoints_exist(self):
        """Test that subcategory endpoints exist"""
        test_category_id = "123e4567-e89b-12d3-a456-426614174000"
        
        # Test list subcategories
        response = self.client.get(f"/api/categories/{test_category_id}/subcategories")
        # Should not return 404 (endpoint should exist)
        assert response.status_code != 404
        
        # Test create subcategory
        response = self.client.post(f"/api/categories/{test_category_id}/subcategories", json={})
        # Should return 422 (validation error) or 401 (auth required), not 404
        assert response.status_code in [422, 401]
    
    def test_franchisor_endpoints_exist(self):
        """Test that franchisor endpoints exist"""
        # Test list franchisors
        response = self.client.get("/api/franchisors")
        # Should not return 404 (endpoint should exist)
        assert response.status_code != 404
        
        # Test create franchisor
        response = self.client.post("/api/franchisors", json={})
        # Should return 422 (validation error) or 401 (auth required), not 404
        assert response.status_code in [422, 401]
    
    def test_error_handling(self):
        """Test basic error handling"""
        # Test 404 for nonexistent endpoint
        response = self.client.get("/api/nonexistent-endpoint-12345")
        assert response.status_code == 404
        
        # Test invalid JSON handling
        response = self.client.post(
            "/api/categories",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        # Should handle gracefully
        assert response.status_code in [422, 400, 401]
    
    def test_security_basics(self):
        """Test basic security measures"""
        # Test SQL injection protection
        malicious_id = "'; DROP TABLE users; --"
        response = self.client.get(f"/api/categories/{malicious_id}")
        # Should handle gracefully, not crash
        assert response.status_code in [400, 404, 422, 401]
        
        # Test XSS protection
        xss_payload = "<script>alert('xss')</script>"
        response = self.client.post("/api/categories", json={"name": xss_payload})
        # Should handle gracefully
        assert response.status_code in [400, 422, 401]


class TestEndpointStructure:
    """Test the structure and organization of endpoints"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_openapi_schema_structure(self):
        """Test that OpenAPI schema has expected structure"""
        response = self.client.get("/openapi.json")
        assert response.status_code == 200
        schema = response.json()
        
        # Check basic structure
        assert "openapi" in schema
        assert "info" in schema
        assert "paths" in schema
        
        # Check that main endpoint groups exist
        paths = schema["paths"]
        
        # Check auth endpoints
        auth_endpoints = [path for path in paths.keys() if "/auth/" in path]
        assert len(auth_endpoints) > 0, "Auth endpoints should exist"
        
        # Check category endpoints
        category_endpoints = [path for path in paths.keys() if "/categories" in path]
        assert len(category_endpoints) > 0, "Category endpoints should exist"
        
        # Check franchisor endpoints
        franchisor_endpoints = [path for path in paths.keys() if "/franchisors" in path]
        assert len(franchisor_endpoints) > 0, "Franchisor endpoints should exist"
    
    def test_endpoint_methods(self):
        """Test that endpoints support expected HTTP methods"""
        response = self.client.get("/openapi.json")
        schema = response.json()
        paths = schema["paths"]
        
        # Check that POST endpoints exist for creation
        post_endpoints = []
        for path, methods in paths.items():
            if "post" in methods:
                post_endpoints.append(path)
        
        assert len(post_endpoints) > 0, "Should have POST endpoints for creation"
        
        # Check that GET endpoints exist for retrieval
        get_endpoints = []
        for path, methods in paths.items():
            if "get" in methods:
                get_endpoints.append(path)
        
        assert len(get_endpoints) > 0, "Should have GET endpoints for retrieval"
    
    def test_response_schemas(self):
        """Test that endpoints have proper response schemas"""
        response = self.client.get("/openapi.json")
        schema = response.json()
        
        # Check that components/schemas exist
        assert "components" in schema
        assert "schemas" in schema["components"]
        
        schemas = schema["components"]["schemas"]
        
        # Check for standard response schemas
        expected_schemas = [
            "StandardResponse",
            "UserResponse", 
            "CategoryResponse",
            "FranchisorResponse"
        ]
        
        for expected_schema in expected_schemas:
            # Check if schema exists (may have variations like StandardResponse_UserResponse_)
            schema_exists = any(expected_schema in schema_name for schema_name in schemas.keys())
            assert schema_exists, f"Schema {expected_schema} should exist"


class TestEndpointSummary:
    """Generate a summary of all available endpoints"""
    
    def test_generate_endpoint_summary(self):
        """Generate and verify endpoint summary"""
        client = TestClient(app)
        response = client.get("/openapi.json")
        assert response.status_code == 200
        schema = response.json()
        
        paths = schema["paths"]
        endpoint_summary = {}
        
        for path, methods in paths.items():
            endpoint_summary[path] = list(methods.keys())
        
        # Print summary for debugging
        print("\n" + "="*80)
        print("ENDPOINT SUMMARY")
        print("="*80)
        
        for path, methods in sorted(endpoint_summary.items()):
            print(f"{path}: {', '.join(methods).upper()}")
        
        print("="*80)
        print(f"Total endpoints: {len(endpoint_summary)}")
        print(f"Total methods: {sum(len(methods) for methods in endpoint_summary.values())}")
        
        # Verify we have a reasonable number of endpoints
        assert len(endpoint_summary) >= 10, "Should have at least 10 endpoints"
        
        # Verify we have the main endpoint categories
        auth_paths = [p for p in paths if "/auth/" in p]
        category_paths = [p for p in paths if "/categories" in p]
        franchisor_paths = [p for p in paths if "/franchisors" in p]
        
        assert len(auth_paths) >= 2, "Should have at least 2 auth endpoints"
        assert len(category_paths) >= 2, "Should have at least 2 category endpoints"
        assert len(franchisor_paths) >= 2, "Should have at least 2 franchisor endpoints"
        
        return endpoint_summary


if __name__ == "__main__":
    # Allow running this file directly
    import subprocess
    import sys
    
    print("🧪 Running endpoint tests without database...")
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        __file__, 
        "-v", "-s"  # -s to show print statements
    ])
    
    if result.returncode == 0:
        print("✅ All endpoint tests passed!")
    else:
        print("❌ Some endpoint tests failed!")
    
    sys.exit(result.returncode)
