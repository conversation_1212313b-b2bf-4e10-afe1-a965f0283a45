"""
Test authentication endpoints
"""
import pytest
import pytest_asyncio
from httpx import AsyncClient, ASGITransport
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from app.main import app
from app.core.database.connection import Base
from app.core.config.settings import settings
from app.models.user import User
from app.core.security import password_hasher
from datetime import datetime, timezone, timedelta
from sqlalchemy import select
from jose import jwt
from app.models.session import Session
from app.core.security.token_blacklist import token_blacklist
from app.core.security.password_validator import password_validator
from fastapi.testclient import TestClient

# Create async engine for tests
engine_test = create_async_engine(settings.DATABASE_URL, echo=True)
TestingSessionLocal = sessionmaker(
    engine_test, class_=AsyncSession, expire_on_commit=False
)

@pytest_asyncio.fixture(scope="session")
async def test_db():
    # Create test database tables
    async with engine_test.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine_test
    
    # Cleanup after all tests
    async with engine_test.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

@pytest_asyncio.fixture
async def db_session(test_db):
    async with TestingSessionLocal() as session:
        yield session
        await session.rollback()

@pytest_asyncio.fixture
async def test_user(db_session):
    user = User(
        email="<EMAIL>",
        password_hash=password_hasher.hash_password("TestPassword123!"),
        is_active=True,
        is_email_verified=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user

@pytest_asyncio.fixture
async def client():
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as ac:
        yield ac

@pytest_asyncio.fixture
async def access_token(client: AsyncClient, test_user):
    payload = {
        "email": test_user.email,
        "password": "TestPassword123!",
        "remember_me": False
    }
    response = await client.post("/api/v1/auth/login", json=payload)
    data = response.json()
    return data["data"]["access_token"]

@pytest.mark.asyncio
async def test_register_user(client: AsyncClient, db_session):
    """Test user registration"""
    response = await client.post(
        "/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "Test123!@#",
            "confirm_password": "Test123!@#"
        }
    )
    assert response.status_code == 201
    data = response.json()
    assert data["success"] is True
    assert data["message"]["title"] == "Registration Successful"
    assert "user" in data["data"]
    assert data["data"]["user"]["email"] == "<EMAIL>"
    assert data["data"]["user"]["is_active"] is True
    assert data["data"]["user"]["is_verified"] is False

@pytest.mark.asyncio
async def test_register_duplicate_email(client: AsyncClient, test_user, db_session):
    """Test registration with duplicate email"""
    response = await client.post(
        "/auth/register",
        json={
            "email": test_user.email,
            "password": "Test123!@#",
            "confirm_password": "Test123!@#"
        }
    )
    assert response.status_code == 409
    data = response.json()
    assert data["success"] is False
    assert data["message"]["title"] == "Registration Failed"
    assert "Email already registered" in data["message"]["description"]

@pytest.mark.asyncio
async def test_login_success(client: AsyncClient, test_user, db_session):
    """Test successful login"""
    response = await client.post(
        "/auth/login",
        json={
            "email": test_user.email,
            "password": "testpassword",
            "remember_me": False
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["message"]["title"] == "Login Successful"
    assert "access_token" in data["data"]
    assert "refresh_token" in data["data"]
    assert data["data"]["token_type"] == "bearer"
    assert "user" in data["data"]
    assert data["data"]["user"]["email"] == test_user.email

@pytest.mark.asyncio
async def test_login_invalid_credentials(client: AsyncClient, test_user, db_session):
    """Test login with invalid credentials"""
    response = await client.post(
        "/auth/login",
        json={
            "email": test_user.email,
            "password": "wrongpassword",
            "remember_me": False
        }
    )
    assert response.status_code == 401
    data = response.json()
    assert data["success"] is False
    assert data["message"]["title"] == "Authentication Failed"
    assert "Invalid email or password" in data["message"]["description"]

@pytest.mark.asyncio
async def test_get_current_user(client: AsyncClient, test_user, auth_headers):
    """Test getting current user info"""
    response = await client.get("/auth/me", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["message"]["title"] == "User Information"
    assert data["data"]["email"] == test_user.email
    assert data["data"]["is_active"] is True

@pytest.mark.asyncio
async def test_get_current_user_unauthorized(client: AsyncClient):
    """Test getting current user without auth"""
    response = await client.get("/auth/me")
    assert response.status_code == 401

@pytest.mark.asyncio
async def test_logout(client: AsyncClient, test_user, auth_headers, db_session):
    """Test user logout"""
    response = await client.post("/auth/logout", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["message"]["title"] == "Logout Successful"

    # Verify token is blacklisted
    token = auth_headers["Authorization"].split(" ")[1]
    assert token_blacklist.add_to_blacklist(token, jti) is None

@pytest.mark.asyncio
async def test_change_password(client: AsyncClient, test_user, auth_headers, db_session):
    """Test password change"""
    response = await client.post(
        "/auth/change-password",
        headers=auth_headers,
        json={
            "current_password": "testpassword",
            "new_password": "NewTest123!@#",
            "confirm_password": "NewTest123!@#"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["message"]["title"] == "Password Changed"

    # Verify new password works
    login_response = await client.post(
        "/auth/login",
        json={
            "email": test_user.email,
            "password": "NewTest123!@#",
            "remember_me": False
        }
    )
    assert login_response.status_code == 200

@pytest.mark.asyncio
async def test_change_password_invalid_current(client: AsyncClient, test_user, auth_headers):
    """Test password change with invalid current password"""
    response = await client.post(
        "/auth/change-password",
        headers=auth_headers,
        json={
            "current_password": "wrongpassword",
            "new_password": "NewTest123!@#",
            "confirm_password": "NewTest123!@#"
        }
    )
    assert response.status_code == 400
    data = response.json()
    assert data["success"] is False
    assert data["message"]["title"] == "Password Change Failed"
    assert "Current password is incorrect" in data["message"]["description"]

@pytest.mark.asyncio
async def test_refresh_token(client: AsyncClient, test_user, db_session):
    """Test token refresh"""
    # First login to get refresh token
    login_response = await client.post(
        "/auth/login",
        json={
            "email": test_user.email,
            "password": "testpassword",
            "remember_me": False
        }
    )
    refresh_token = login_response.json()["data"]["refresh_token"]

    # Use refresh token to get new access token
    response = await client.post(
        "/auth/refresh-token",
        json={"refresh_token": refresh_token}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["message"]["title"] == "Token Refreshed"
    assert "access_token" in data["data"]
    assert data["data"]["token_type"] == "bearer"

@pytest.mark.asyncio
async def test_refresh_token_invalid(client: AsyncClient):
    """Test token refresh with invalid token"""
    response = await client.post(
        "/auth/refresh-token",
        json={"refresh_token": "invalid_token"}
    )
    assert response.status_code == 401
    data = response.json()
    assert data["success"] is False
    assert data["message"]["title"] == "Token Refresh Failed"
    assert "Invalid or expired refresh token" in data["message"]["description"]

@pytest.mark.asyncio
async def test_get_user_sessions(client: AsyncClient, test_user, auth_headers, db_session):
    """Test getting user sessions"""
    response = await client.get("/auth/sessions", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["message"]["title"] == "Sessions Retrieved"
    assert "sessions" in data["data"]

@pytest.mark.asyncio
async def test_end_user_session(client: AsyncClient, test_user, auth_headers, db_session):
    """Test ending a user session"""
    # First get sessions
    sessions_response = await client.get("/auth/sessions", headers=auth_headers)
    sessions = sessions_response.json()["data"]["sessions"]
    session_id = sessions[0]["id"]

    # End the session
    response = await client.delete(f"/auth/sessions/{session_id}", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["message"]["title"] == "Session Ended"

@pytest.mark.asyncio
async def test_verify_email(client: AsyncClient, test_user, db_session):
    """Test email verification"""
    # Generate verification token
    verification_token = "test_verification_token"
    test_user.email_verification_token = verification_token
    test_user.email_verification_expires = datetime.now(timezone.utc) + timedelta(hours=24)
    await db_session.commit()

    response = await client.post(f"/auth/verify-email/{verification_token}")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["message"]["title"] == "Email Verified"

    # Verify user is marked as verified
    await db_session.refresh(test_user)
    assert test_user.is_email_verified is True

@pytest.mark.asyncio
async def test_resend_verification(client: AsyncClient, test_user, auth_headers, db_session):
    """Test resending verification email"""
    # Set user as unverified
    test_user.is_email_verified = False
    await db_session.commit()

    response = await client.post("/auth/resend-verification", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["message"]["title"] == "Verification Email Sent"

@pytest.mark.asyncio
async def test_forgot_password(client: AsyncClient, test_user, db_session):
    """Test forgot password flow"""
    response = await client.post(
        "/auth/forgot-password",
        json={"email": test_user.email}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["message"]["title"] == "Reset Email Sent"

    # Verify reset token was generated
    await db_session.refresh(test_user)
    assert test_user.password_reset_token is not None
    assert test_user.password_reset_expires is not None

@pytest.mark.asyncio
async def test_reset_password(client: AsyncClient, test_user, db_session):
    """Test password reset"""
    # Generate reset token
    reset_token = "test_reset_token"
    test_user.password_reset_token = reset_token
    test_user.password_reset_expires = datetime.now(timezone.utc) + timedelta(hours=1)
    await db_session.commit()

    response = await client.post(
        "/auth/reset-password",
        json={
            "token": reset_token,
            "new_password": "NewTest123!@#",
            "confirm_password": "NewTest123!@#"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["message"]["title"] == "Password Reset"

    # Verify new password works
    login_response = await client.post(
        "/auth/login",
        json={
            "email": test_user.email,
            "password": "NewTest123!@#",
            "remember_me": False
        }
    )
    assert login_response.status_code == 200

@pytest.mark.asyncio
async def test_reset_password_invalid_token(client: AsyncClient):
    """Test password reset with invalid token"""
    response = await client.post(
        "/auth/reset-password",
        json={
            "token": "invalid_token",
            "new_password": "NewTest123!@#",
            "confirm_password": "NewTest123!@#"
        }
    )
    assert response.status_code == 400
    data = response.json()
    assert data["success"] is False
    assert data["message"]["title"] == "Password Reset Failed"
    assert "Invalid or expired reset token" in data["message"]["description"]

# Fixtures for AsyncClient and access_token
def pytest_configure():
    import asyncio
    asyncio.get_event_loop().set_debug(True)

@pytest.fixture
def test_client():
    """Create a test client"""
    return TestClient(app)

def test_login_endpoint(test_client):
    """Test login endpoint"""
    response = test_client.post(
        "/api/auth/login",
        json={"username": "<EMAIL>", "password": "testpass123"}
    )
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert "token_type" in data
    assert data["token_type"] == "bearer" 