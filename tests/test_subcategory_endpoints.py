import pytest
from httpx import AsyncClient
from fastapi import status
from app.main import app

@pytest.mark.asyncio
async def test_create_subcategory_success(authenticated_client: AsyncClient, category_id):
    payload = {
        "name": "Test Subcategory",
        "description": "A test subcategory."
    }
    response = await authenticated_client.post(f"/api/categories/{category_id}/subcategories", json=payload)
    assert response.status_code == status.HTTP_201_CREATED
    data = response.json()
    assert data["success"] is True
    assert data["data"]["name"] == payload["name"]
    assert data["data"]["category_id"] == category_id

@pytest.mark.asyncio
async def test_create_subcategory_duplicate(authenticated_client: AsyncClient, category_id):
    payload = {
        "name": "Test Subcategory",
        "description": "Duplicate test subcategory."
    }
    # First create
    await authenticated_client.post(f"/api/categories/{category_id}/subcategories", json=payload)
    # Duplicate create
    response = await authenticated_client.post(f"/api/categories/{category_id}/subcategories", json=payload)
    assert response.status_code == status.HTTP_409_CONFLICT
    data = response.json()
    assert data["success"] is False
    assert "already exists" in data["message"]["description"]

@pytest.mark.asyncio
async def test_get_subcategory_success(authenticated_client: AsyncClient, category_id):
    payload = {
        "name": "Get Subcategory",
        "description": "Subcategory to get."
    }
    create_resp = await authenticated_client.post(f"/api/categories/{category_id}/subcategories", json=payload)
    subcategory_id = create_resp.json()["data"]["id"]
    response = await authenticated_client.get(f"/api/subcategories/{subcategory_id}")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert data["data"]["id"] == subcategory_id

@pytest.mark.asyncio
async def test_get_subcategory_not_found(authenticated_client: AsyncClient):
    response = await authenticated_client.get("/api/subcategories/999999")
    assert response.status_code == status.HTTP_404_NOT_FOUND
    data = response.json()
    assert data["success"] is False
    assert "not found" in data["message"]["description"].lower()

@pytest.mark.asyncio
async def test_update_subcategory_success(authenticated_client: AsyncClient, category_id):
    payload = {
        "name": "Update Subcategory",
        "description": "Subcategory to update."
    }
    create_resp = await authenticated_client.post(f"/api/categories/{category_id}/subcategories", json=payload)
    subcategory_id = create_resp.json()["data"]["id"]
    update_payload = {"description": "Updated description."}
    response = await authenticated_client.put(f"/api/subcategories/{subcategory_id}", json=update_payload)
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert data["data"]["description"] == update_payload["description"]

@pytest.mark.asyncio
async def test_list_subcategories(authenticated_client: AsyncClient, category_id):
    response = await authenticated_client.get(f"/api/categories/{category_id}/subcategories")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert isinstance(data["data"], list) 