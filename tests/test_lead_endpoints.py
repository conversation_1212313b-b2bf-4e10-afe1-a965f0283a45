"""Test lead management endpoints"""
import pytest
import pytest_asyncio
import io
import csv
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import delete
from app.main import app
from app.models.lead import Lead, Communication
from app.models.user import User
from app.core.database.connection import get_db
from datetime import datetime, timedelta


class CustomTestClient(TestClient):
    def request(self, *args, **kwargs):
        headers = kwargs.pop('headers', {}) or {}
        headers.setdefault('host', 'localhost')
        kwargs['headers'] = headers
        return super().request(*args, **kwargs)


@pytest.fixture
def client(db_session):
    # Override get_db before app starts
    async def _override_get_db():
        yield db_session
    app.dependency_overrides[get_db] = _override_get_db
    with CustomTestClient(app) as c:
        yield c
    app.dependency_overrides.clear()


@pytest_asyncio.fixture
async def authenticated_client(client: TestClient, test_user: User):
    # Login to get token
    response = client.post(
        "/api/v1/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "testpassword",
            "remember_me": False
        }
    )
    assert response.status_code == 200
    data = response.json()
    token = data["data"]["access_token"]
    client.headers["Authorization"] = f"Bearer {token}"
    return client


@pytest_asyncio.fixture
async def sample_lead(db_session: AsyncSession):
    # Clean up any existing leads
    await db_session.execute(delete(Lead))
    await db_session.commit()

    lead = Lead(
        full_name="John Doe",
        contact_number="+1234567890",
        email="<EMAIL>",
        location="New York",
        lead_source="Website",
        franchise_preference="Food",
        budget_preference=75000.00,
        qualification_status="new"
    )
    db_session.add(lead)
    await db_session.commit()
    await db_session.refresh(lead)
    return lead


class TestLeadEndpoints:
    """Test lead management endpoints"""
    
    def test_list_leads_basic(self, authenticated_client: TestClient, sample_lead: Lead):
        """Test basic lead listing"""
        response = authenticated_client.get("/api/v1/leads/")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "items" in data["data"]
        assert len(data["data"]["items"]) >= 1
    
    def test_list_leads_with_search(self, authenticated_client: TestClient, sample_lead: Lead):
        """Test lead listing with search"""
        response = authenticated_client.get("/api/v1/leads/?search=John")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["data"]["items"]) >= 1
        assert "John" in data["data"]["items"][0]["full_name"]
    
    def test_list_leads_with_filters(self, authenticated_client: TestClient, sample_lead: Lead):
        """Test lead listing with filters"""
        response = authenticated_client.get("/api/v1/leads/?status=new&lead_source=Website")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_list_leads_with_sorting(self, authenticated_client: TestClient, sample_lead: Lead):
        """Test lead listing with sorting"""
        response = authenticated_client.get("/api/v1/leads/?sort=name_asc")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    def test_create_lead(self, authenticated_client: TestClient):
        """Test lead creation"""
        lead_data = {
            "full_name": "Jane Smith",
            "contact_number": "+1987654321",
            "email": "<EMAIL>",
            "location": "California",
            "lead_source": "Referral",
            "franchise_preference": "Retail",
            "budget_preference": 100000.00,
            "qualification_status": "new"
        }
        response = authenticated_client.post("/api/v1/leads/", json=lead_data)
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["full_name"] == "Jane Smith"
        assert data["data"]["contact_number"] == "+1987654321"
    
    def test_get_lead_by_id(self, authenticated_client: TestClient, sample_lead: Lead):
        """Test getting lead by ID"""
        response = authenticated_client.get(f"/api/v1/leads/{sample_lead.id}")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["id"] == str(sample_lead.id)
        assert data["data"]["full_name"] == sample_lead.full_name
    
    def test_update_lead(self, authenticated_client: TestClient, sample_lead: Lead):
        """Test lead update"""
        update_data = {
            "full_name": "John Updated",
            "qualification_status": "qualified"
        }
        response = authenticated_client.put(f"/api/v1/leads/{sample_lead.id}", json=update_data)
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["full_name"] == "John Updated"
        assert data["data"]["qualification_status"] == "qualified"
    
    def test_delete_lead(self, authenticated_client: TestClient, sample_lead: Lead):
        """Test lead deletion"""
        response = authenticated_client.delete(f"/api/v1/leads/{sample_lead.id}")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        
        # Verify lead is deleted
        get_response = authenticated_client.get(f"/api/v1/leads/{sample_lead.id}")
        assert get_response.status_code == 404


class TestBulkUpload:
    """Test bulk upload functionality"""
    
    def create_test_csv(self, leads_data):
        """Helper to create CSV content"""
        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=[
            'full_name', 'contact_number', 'email', 'location',
            'lead_source', 'franchise_preference', 'budget_preference',
            'qualification_status', 'is_active', 'is_deleted'
        ])
        writer.writeheader()
        for lead in leads_data:
            writer.writerow(lead)
        return output.getvalue()
    
    def test_bulk_upload_success(self, authenticated_client: TestClient):
        """Test successful bulk upload"""
        leads_data = [
            {
                'full_name': 'Alice Johnson',
                'contact_number': '+1111111111',
                'email': '<EMAIL>',
                'location': 'Texas',
                'lead_source': 'Website',
                'franchise_preference': 'Food',
                'budget_preference': '50000',
                'qualification_status': 'new',
                'is_active': 'true',
                'is_deleted': 'false'
            },
            {
                'full_name': 'Bob Wilson',
                'contact_number': '+2222222222',
                'email': '<EMAIL>',
                'location': 'Florida',
                'lead_source': 'Referral',
                'franchise_preference': 'Retail',
                'budget_preference': '75000',
                'qualification_status': 'new',
                'is_active': '1',
                'is_deleted': '0'
            }
        ]
        
        csv_content = self.create_test_csv(leads_data)
        csv_file = io.BytesIO(csv_content.encode('utf-8'))
        
        response = authenticated_client.post(
            "/api/v1/leads/bulk-upload",
            files={"file": ("test_leads.csv", csv_file, "text/csv")}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["successful_imports"] == 2
        assert data["data"]["duplicates_found"] == 0
        assert data["data"]["errors_found"] == 0
    
    def test_bulk_upload_with_duplicates(self, authenticated_client: TestClient, sample_lead: Lead):
        """Test bulk upload with duplicate detection"""
        leads_data = [
            {
                'full_name': 'Duplicate Lead',
                'contact_number': '+1234567890',  # Same as sample_lead
                'email': '<EMAIL>',
                'location': 'Texas',
                'lead_source': 'Website',
                'franchise_preference': 'Food',
                'budget_preference': '50000',
                'qualification_status': 'new',
                'is_active': 'true',
                'is_deleted': 'false'
            }
        ]
        
        csv_content = self.create_test_csv(leads_data)
        csv_file = io.BytesIO(csv_content.encode('utf-8'))
        
        response = authenticated_client.post(
            "/api/v1/leads/bulk-upload",
            files={"file": ("test_leads.csv", csv_file, "text/csv")}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["duplicates_found"] >= 1
    
    def test_bulk_upload_invalid_file(self, authenticated_client: TestClient):
        """Test bulk upload with invalid file"""
        response = authenticated_client.post(
            "/api/v1/leads/bulk-upload",
            files={"file": ("test.txt", io.BytesIO(b"not a csv"), "text/plain")}
        )
        
        assert response.status_code == 400
    
    def test_bulk_upload_missing_headers(self, authenticated_client: TestClient):
        """Test bulk upload with missing required headers"""
        csv_content = "name,phone\nJohn,123456"
        csv_file = io.BytesIO(csv_content.encode('utf-8'))
        
        response = authenticated_client.post(
            "/api/v1/leads/bulk-upload",
            files={"file": ("test_leads.csv", csv_file, "text/csv")}
        )
        
        assert response.status_code == 422

    def test_bulk_upload_with_boolean_fields(self, authenticated_client: TestClient):
        """Test bulk upload with various boolean field formats"""
        leads_data = [
            {
                'full_name': 'Test User 1',
                'contact_number': '+1333333333',
                'email': '<EMAIL>',
                'location': 'California',
                'lead_source': 'Website',
                'franchise_preference': 'Food',
                'budget_preference': '60000',
                'qualification_status': 'new',
                'is_active': 'true',
                'is_deleted': 'false'
            },
            {
                'full_name': 'Test User 2',
                'contact_number': '+1444444444',
                'email': '<EMAIL>',
                'location': 'Texas',
                'lead_source': 'Referral',
                'franchise_preference': 'Retail',
                'budget_preference': '80000',
                'qualification_status': 'contacted',
                'is_active': '1',
                'is_deleted': '0'
            },
            {
                'full_name': 'Test User 3',
                'contact_number': '+**********',
                'email': '<EMAIL>',
                'location': 'Florida',
                'lead_source': 'Social Media',
                'franchise_preference': 'Health',
                'budget_preference': '90000',
                'qualification_status': 'qualified',
                'is_active': 'yes',
                'is_deleted': 'no'
            }
        ]

        csv_content = self.create_test_csv(leads_data)
        csv_file = io.BytesIO(csv_content.encode('utf-8'))

        response = authenticated_client.post(
            "/api/v1/leads/bulk-upload",
            files={"file": ("test_leads.csv", csv_file, "text/csv")}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["successful_imports"] == 3
        assert data["data"]["duplicates_found"] == 0
        assert data["data"]["errors_found"] == 0


class TestCommunicationHistory:
    """Test communication history functionality"""
    
    def test_get_communication_history_empty(self, authenticated_client: TestClient, sample_lead: Lead):
        """Test getting communication history for lead with no communications"""
        response = authenticated_client.get(f"/api/v1/leads/{sample_lead.id}/history")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["total_count"] == 0
        assert len(data["data"]["communications"]) == 0
    
    def test_get_communication_history_not_found(self, authenticated_client: TestClient):
        """Test getting communication history for non-existent lead"""
        fake_id = "550e8400-e29b-41d4-a716-446655440000"
        response = authenticated_client.get(f"/api/v1/leads/{fake_id}/history")
        assert response.status_code == 404
