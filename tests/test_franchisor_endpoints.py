"""
Unit tests for Franchisor Management endpoints
"""

import pytest
import json
import io
import csv
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession
from app.main import app
from app.models.franchisor import Franchisor
from app.schemas.franchisor import FranchisorRegion

client = TestClient(app)

# Test data
SAMPLE_FRANCHISOR_DATA = {
    "name": "Test Coffee Shop",
    "category_id": "123e4567-e89b-12d3-a456-426614174000",
    "subcategory_id": "123e4567-e89b-12d3-a456-426614174001",
    "region": "australia",
    "budget": 250000.0,
    "is_active": True
}

SAMPLE_FRANCHISOR_UPDATE_DATA = {
    "name": "Updated Coffee Shop",
    "budget": 300000.0,
    "is_active": False
}

@pytest.fixture
def mock_auth_user():
    """Mock authenticated user"""
    return {
        "user_id": "test_user_123",
        "email": "<EMAIL>",
        "role": "ADMIN",
        "is_active": True,
        "auth_method": "jwt"
    }

@pytest.fixture
def mock_db_session():
    """Mock database session"""
    session = AsyncMock(spec=AsyncSession)
    return session

@pytest.fixture
def sample_franchisor():
    """Sample franchisor model instance"""
    return Franchisor(
        id="frc_test_123",
        name="Test Coffee Shop",
        category="food_beverage",
        region="australia",
        budget=250000.0,
        sub_category="cafe",
        is_active=True
    )

class TestFranchisorEndpoints:
    """Test cases for franchisor endpoints"""

    @patch('app.api.v1.endpoints.franchisors.get_current_user')
    @patch('app.api.v1.endpoints.franchisors.FranchisorService')
    def test_list_franchisors_success(self, mock_service_class, mock_get_current_user, mock_auth_user):
        """Test successful franchisor listing"""
        # Setup mocks
        mock_get_current_user.return_value = mock_auth_user
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        
        # Mock service responses as async methods
        mock_service.get_franchisors = AsyncMock(return_value=[])
        mock_service.count_franchisors = AsyncMock(return_value=0)
        
        # Make request
        response = client.get("/api/franchisors/")
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "Franchisors Retrieved" in data["message"]["title"]
        assert "items" in data["data"]
        assert "pagination" in data["data"]

    @patch('app.api.v1.endpoints.franchisors.get_current_user')
    def test_list_franchisors_unauthorized(self, mock_get_current_user):
        """Test franchisor listing without authentication"""
        # Setup mock to raise authentication error
        mock_get_current_user.side_effect = Exception("Authentication required")
        
        # Make request
        response = client.get("/api/franchisors/")
        
        # Assertions
        assert response.status_code == 401

    @patch('app.api.v1.endpoints.franchisors.get_current_user')
    @patch('app.api.v1.endpoints.franchisors.FranchisorService')
    def test_get_franchisor_by_id_success(self, mock_service_class, mock_get_current_user, mock_auth_user, sample_franchisor):
        """Test successful franchisor retrieval by ID"""
        # Setup mocks
        mock_get_current_user.return_value = mock_auth_user
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.get_franchisor_by_id = AsyncMock(return_value=sample_franchisor)
        
        # Make request
        response = client.get("/api/franchisors/frc_test_123")
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["id"] == "frc_test_123"
        assert data["data"]["name"] == "Test Coffee Shop"

    @patch('app.api.v1.endpoints.franchisors.get_current_user')
    @patch('app.api.v1.endpoints.franchisors.FranchisorService')
    def test_get_franchisor_not_found(self, mock_service_class, mock_get_current_user, mock_auth_user):
        """Test franchisor retrieval with non-existent ID"""
        # Setup mocks
        mock_get_current_user.return_value = mock_auth_user
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.get_franchisor_by_id = AsyncMock(return_value=None)
        
        # Make request
        response = client.get("/api/franchisors/nonexistent_id")
        
        # Assertions
        assert response.status_code == 404

    @patch('app.api.v1.endpoints.franchisors.get_current_user')
    @patch('app.api.v1.endpoints.franchisors.FranchisorService')
    def test_create_franchisor_success(self, mock_service_class, mock_get_current_user, mock_auth_user, sample_franchisor):
        """Test successful franchisor creation"""
        # Setup mocks
        mock_get_current_user.return_value = mock_auth_user
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.create_franchisor = AsyncMock(return_value=sample_franchisor)
        
        # Make request
        response = client.post(
            "/api/franchisors/",
            json=SAMPLE_FRANCHISOR_DATA,
            headers={"Authorization": "Bearer test_token"}
        )
        
        # Assertions
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert data["data"]["name"] == "Test Coffee Shop"

    @patch('app.api.v1.endpoints.franchisors.get_current_user')
    @patch('app.api.v1.endpoints.franchisors.FranchisorService')
    def test_create_franchisor_validation_error(self, mock_service_class, mock_get_current_user, mock_auth_user):
        """Test franchisor creation with invalid data"""
        # Setup mocks
        mock_get_current_user.return_value = mock_auth_user
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.create_franchisor = AsyncMock(side_effect=ValueError("Invalid data"))
        
        # Make request with invalid data
        invalid_data = {"name": "", "category": "invalid_category"}
        response = client.post(
            "/api/franchisors/",
            json=invalid_data,
            headers={"Authorization": "Bearer test_token"}
        )
        
        # Assertions
        assert response.status_code == 422

    @patch('app.api.v1.endpoints.franchisors.get_current_user')
    @patch('app.api.v1.endpoints.franchisors.FranchisorService')
    def test_update_franchisor_success(self, mock_service_class, mock_get_current_user, mock_auth_user, sample_franchisor):
        """Test successful franchisor update"""
        # Setup mocks
        mock_get_current_user.return_value = mock_auth_user
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.update_franchisor = AsyncMock(return_value=sample_franchisor)
        
        # Make request
        response = client.put(
            "/api/franchisors/frc_test_123",
            json=SAMPLE_FRANCHISOR_UPDATE_DATA,
            headers={"Authorization": "Bearer test_token"}
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    @patch('app.api.v1.endpoints.franchisors.get_current_user')
    @patch('app.api.v1.endpoints.franchisors.FranchisorService')
    def test_delete_franchisor_success(self, mock_service_class, mock_get_current_user, mock_auth_user):
        """Test successful franchisor deletion"""
        # Setup mocks
        mock_get_current_user.return_value = mock_auth_user
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.delete_franchisor = AsyncMock(return_value=True)
        
        # Make request
        response = client.delete(
            "/api/franchisors/frc_test_123",
            headers={"Authorization": "Bearer test_token"}
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "deleted" in data["data"]["message"].lower()

    @patch('app.api.v1.endpoints.franchisors.get_current_user')
    @patch('app.api.v1.endpoints.franchisors.FranchisorService')
    def test_import_franchisors_success(self, mock_service_class, mock_get_current_user, mock_auth_user):
        """Test successful CSV import"""
        # Setup mocks
        mock_get_current_user.return_value = mock_auth_user
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        
        # Mock import result
        import_result = {
            "total_rows": 3,
            "successful_imports": 2,
            "failed_imports": 1,
            "errors": [{"row": 3, "field": "name", "message": "Name is required"}]
        }
        mock_service.import_franchisors_from_csv = AsyncMock(return_value=import_result)
        
        # Create CSV file
        csv_data = [
            ["name", "category", "region", "budget", "sub_category"],
            ["Coffee Shop 1", "food_beverage", "australia", "250000", "cafe"],
            ["Coffee Shop 2", "food_beverage", "australia", "300000", "cafe"],
            ["", "food_beverage", "australia", "200000", "cafe"]  # Invalid row
        ]
        
        csv_file = io.StringIO()
        writer = csv.writer(csv_file)
        writer.writerows(csv_data)
        csv_file.seek(0)
        
        # Make request
        files = {"file": ("test.csv", csv_file.getvalue(), "text/csv")}
        response = client.post(
            "/api/franchisors/import",
            files=files,
            headers={"Authorization": "Bearer test_token"}
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["total_rows"] == 3
        assert data["data"]["successful_imports"] == 2
        assert data["data"]["failed_imports"] == 1

    @patch('app.api.v1.endpoints.franchisors.get_current_user')
    @patch('app.api.v1.endpoints.franchisors.FranchisorService')
    @patch('app.api.v1.endpoints.franchisors.S3Service')
    def test_upload_brochure_success(self, mock_s3_class, mock_service_class, mock_get_current_user, mock_auth_user, sample_franchisor):
        """Test successful brochure upload"""
        # Setup mocks
        mock_get_current_user.return_value = mock_auth_user
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        
        mock_s3_service = Mock()
        mock_s3_class.return_value = mock_s3_service
        mock_s3_service.upload_file = AsyncMock(return_value="https://s3.amazonaws.com/bucket/brochure.pdf")
        
        # Update franchisor with brochure URL
        sample_franchisor.brochure_url = "https://s3.amazonaws.com/bucket/brochure.pdf"
        mock_service.upload_brochure = AsyncMock(return_value=sample_franchisor)
        
        # Create test file
        test_file_content = b"test brochure content"
        
        # Make request
        files = {"file": ("test_brochure.pdf", test_file_content, "application/pdf")}
        response = client.post(
            "/api/franchisors/frc_test_123/upload-brochure",
            files=files,
            headers={"Authorization": "Bearer test_token"}
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["brochure_url"] == "https://s3.amazonaws.com/bucket/brochure.pdf"

    def test_franchisor_schemas_validation(self):
        """Test Pydantic schema validation"""
        from app.schemas.franchisor import FranchisorCreateRequest, FranchisorUpdateRequest

        # Test valid data
        valid_data = SAMPLE_FRANCHISOR_DATA
        franchisor_request = FranchisorCreateRequest(**valid_data)
        assert franchisor_request.name == "Test Coffee Shop"
        assert franchisor_request.category_id == "123e4567-e89b-12d3-a456-426614174000"

        # Test with missing name
        with pytest.raises(ValueError):
            invalid_data = valid_data.copy()
            del invalid_data["name"]
            FranchisorCreateRequest(**invalid_data)

    @patch('app.api.v1.endpoints.franchisors.get_current_user')
    def test_franchisor_endpoints_require_auth(self, mock_get_current_user):
        """Test that all protected endpoints require authentication"""
        # Setup mock to raise authentication error
        mock_get_current_user.side_effect = Exception("Authentication required")
        
        # Test all protected endpoints
        protected_endpoints = [
            ("POST", "/api/franchisors/"),
            ("PUT", "/api/franchisors/frc_test_123"),
            ("DELETE", "/api/franchisors/frc_test_123"),
            ("POST", "/api/franchisors/import"),
            ("POST", "/api/franchisors/frc_test_123/upload-brochure"),
        ]
        
        for method, endpoint in protected_endpoints:
            if method == "POST" and "import" in endpoint:
                # For file upload endpoints, we need to provide a file
                files = {"file": ("test.csv", "test content", "text/csv")}
                response = client.post(endpoint, files=files)
            elif method == "POST" and "upload-brochure" in endpoint:
                files = {"file": ("test.pdf", b"test content", "application/pdf")}
                response = client.post(endpoint, files=files)
            else:
                response = client.request(method, endpoint, json=SAMPLE_FRANCHISOR_DATA)
            
            assert response.status_code == 401, f"Endpoint {method} {endpoint} should require authentication"


class TestFranchisorService:
    """Test cases for FranchisorService"""

    @pytest.mark.asyncio
    async def test_create_franchisor_service(self, mock_db_session):
        """Test franchisor creation in service layer"""
        from app.services.franchisor_service import FranchisorService
        from app.schemas.franchisor import FranchisorCreateRequest
        
        # Setup
        service = FranchisorService(mock_db_session)
        mock_db_session.add = AsyncMock()
        mock_db_session.commit = AsyncMock()
        mock_db_session.refresh = AsyncMock()
        
        # Create proper Pydantic model
        franchisor_request = FranchisorCreateRequest(**SAMPLE_FRANCHISOR_DATA)
        
        # Execute
        result = await service.create_franchisor(franchisor_request)
        
        # Assertions
        assert result is not None
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_franchisors_with_filters(self, mock_db_session):
        """Test franchisor retrieval with filters"""
        from app.services.franchisor_service import FranchisorService
        from app.schemas.franchisor import FranchisorCategory, FranchisorRegion
        
        # Setup
        service = FranchisorService(mock_db_session)
        mock_db_session.execute = AsyncMock()
        
        # Execute with proper enum values
        result = await service.get_franchisors(
            category=FranchisorCategory.FOOD_BEVERAGE.value,
            region=FranchisorRegion.AUSTRALIA.value,
            is_active=True,
            search="coffee",
            skip=0,
            limit=10
        )
        
        # Assertions
        assert result is not None
        mock_db_session.execute.assert_called()


class TestFranchisorModel:
    """Test cases for Franchisor model"""

    def test_franchisor_model_creation(self):
        """Test franchisor model instance creation"""
        franchisor = Franchisor(
            id="frc_test_123",
            name="Test Franchisor",
            category_id="123e4567-e89b-12d3-a456-426614174000",
            subcategory_id="123e4567-e89b-12d3-a456-426614174001",
            region="australia",
            budget=250000.0,
            is_active=True
        )

        assert franchisor.id == "frc_test_123"
        assert franchisor.name == "Test Franchisor"
        assert franchisor.category_id == "123e4567-e89b-12d3-a456-426614174000"
        assert franchisor.budget == 250000.0
        assert franchisor.is_active is True

    def test_franchisor_model_validation(self):
        """Test franchisor model validation"""
        # Test valid data
        franchisor = Franchisor(
            id="frc_test_123",
            name="Test Franchisor",
            category_id="123e4567-e89b-12d3-a456-426614174000",
            region="australia",
            budget=250000.0
        )

        assert franchisor.name is not None
        assert franchisor.category_id is not None

        # Test that optional fields can be None
        assert franchisor.subcategory_id is None
        assert franchisor.brochure_url is None


if __name__ == "__main__":
    pytest.main([__file__]) 