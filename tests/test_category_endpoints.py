import pytest
from httpx import AsyncClient
from fastapi import status
from app.main import app

@pytest.mark.asyncio
async def test_create_category_success(authenticated_client: AsyncClient):
    payload = {
        "name": "Test Category",
        "description": "A test category."
    }
    response = await authenticated_client.post("/api/categories", json=payload)
    assert response.status_code == status.HTTP_201_CREATED
    data = response.json()
    assert data["success"] is True
    assert data["data"]["name"] == payload["name"]

@pytest.mark.asyncio
async def test_create_category_duplicate(authenticated_client: AsyncClient):
    payload = {
        "name": "Test Category",
        "description": "Duplicate test category."
    }
    # First create
    await authenticated_client.post("/api/categories", json=payload)
    # Duplicate create
    response = await authenticated_client.post("/api/categories", json=payload)
    assert response.status_code == status.HTTP_409_CONFLICT
    data = response.json()
    assert data["success"] is False
    assert "already exists" in data["message"]["description"]

@pytest.mark.asyncio
async def test_get_category_success(authenticated_client: AsyncClient):
    payload = {
        "name": "Get Category",
        "description": "Category to get."
    }
    create_resp = await authenticated_client.post("/api/categories", json=payload)
    category_id = create_resp.json()["data"]["id"]
    response = await authenticated_client.get(f"/api/categories/{category_id}")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert data["data"]["id"] == category_id

@pytest.mark.asyncio
async def test_get_category_not_found(authenticated_client: AsyncClient):
    response = await authenticated_client.get("/api/categories/999999")
    assert response.status_code == status.HTTP_404_NOT_FOUND
    data = response.json()
    assert data["success"] is False
    assert "not found" in data["message"]["description"].lower()

@pytest.mark.asyncio
async def test_update_category_success(authenticated_client: AsyncClient):
    payload = {
        "name": "Update Category",
        "description": "Category to update."
    }
    create_resp = await authenticated_client.post("/api/categories", json=payload)
    category_id = create_resp.json()["data"]["id"]
    update_payload = {"description": "Updated description."}
    response = await authenticated_client.put(f"/api/categories/{category_id}", json=update_payload)
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert data["data"]["description"] == update_payload["description"]

@pytest.mark.asyncio
async def test_list_categories(authenticated_client: AsyncClient):
    response = await authenticated_client.get("/api/categories")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert isinstance(data["data"], list)

@pytest.mark.asyncio
async def test_create_category_validation_error(authenticated_client: AsyncClient):
    """Test that missing required field returns a StandardResponse-style validation error."""
    print("\n[TEST] Starting: test_create_category_validation_error - should return 422 and StandardResponse error format.")
    payload = {"description": "Missing name field"}  # 'name' is required
    response = await authenticated_client.post("/api/categories", json=payload)
    print(f"[TEST] Response status: {response.status_code}")
    data = response.json()
    print(f"[TEST] Response data: {data}")
    assert response.status_code == 422
    assert data["success"] is False
    assert data["error_code"] == 422
    assert "Validation Error" in data["message"]["title"]
    assert "name" in data["message"]["description"]
    print("[TEST] PASSED: test_create_category_validation_error - StandardResponse error returned for missing required field.") 