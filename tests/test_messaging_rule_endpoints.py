"""
Test Messaging Rule Endpoints
Comprehensive tests for messaging rule configuration API
"""

import pytest
from httpx import AsyncClient
from fastapi import status
from app.main import app


@pytest.mark.asyncio
async def test_create_messaging_rule_success(authenticated_client: AsyncClient):
    """Test successful messaging rule creation"""
    payload = {
        "lead_init_delay_h": 2,
        "no_response_delay_h": 24,
        "max_followups": 3
    }
    response = await authenticated_client.post("/api/settings/messaging-rules", json=payload)
    assert response.status_code == status.HTTP_201_CREATED
    data = response.json()
    assert data["success"] is True
    assert data["data"]["lead_init_delay_h"] == payload["lead_init_delay_h"]
    assert data["data"]["no_response_delay_h"] == payload["no_response_delay_h"]
    assert data["data"]["max_followups"] == payload["max_followups"]
    assert data["data"]["is_active"] is True


@pytest.mark.asyncio
async def test_create_messaging_rule_single_active_enforcement(authenticated_client: AsyncClient):
    """Test that creating a new rule deactivates existing active rules"""
    # Create first rule
    payload1 = {
        "lead_init_delay_h": 1,
        "no_response_delay_h": 12,
        "max_followups": 2
    }
    response1 = await authenticated_client.post("/api/settings/messaging-rules", json=payload1)
    assert response1.status_code == status.HTTP_201_CREATED
    rule1_id = response1.json()["data"]["id"]
    
    # Create second rule (should deactivate first)
    payload2 = {
        "lead_init_delay_h": 4,
        "no_response_delay_h": 48,
        "max_followups": 5
    }
    response2 = await authenticated_client.post("/api/settings/messaging-rules", json=payload2)
    assert response2.status_code == status.HTTP_201_CREATED
    rule2_id = response2.json()["data"]["id"]
    
    # Check that only the second rule is active
    active_response = await authenticated_client.get("/api/settings/messaging-rules/active")
    assert active_response.status_code == status.HTTP_200_OK
    active_data = active_response.json()
    assert active_data["data"]["id"] == rule2_id
    
    # Check that first rule is now inactive
    rule1_response = await authenticated_client.get(f"/api/settings/messaging-rules/{rule1_id}")
    assert rule1_response.status_code == status.HTTP_200_OK
    rule1_data = rule1_response.json()
    assert rule1_data["data"]["is_active"] is False


@pytest.mark.asyncio
async def test_create_messaging_rule_invalid_constraints(authenticated_client: AsyncClient):
    """Test creating messaging rule with invalid constraints"""
    invalid_payloads = [
        {
            "lead_init_delay_h": -1,  # Must be >= 0
            "no_response_delay_h": 24,
            "max_followups": 3
        },
        {
            "lead_init_delay_h": 2,
            "no_response_delay_h": 0,  # Must be > 0
            "max_followups": 3
        },
        {
            "lead_init_delay_h": 2,
            "no_response_delay_h": 24,
            "max_followups": -1  # Must be >= 0
        }
    ]
    
    for payload in invalid_payloads:
        response = await authenticated_client.post("/api/settings/messaging-rules", json=payload)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.asyncio
async def test_get_messaging_rule_success(authenticated_client: AsyncClient):
    """Test successful messaging rule retrieval"""
    payload = {
        "lead_init_delay_h": 3,
        "no_response_delay_h": 36,
        "max_followups": 4
    }
    create_resp = await authenticated_client.post("/api/settings/messaging-rules", json=payload)
    rule_id = create_resp.json()["data"]["id"]
    
    response = await authenticated_client.get(f"/api/settings/messaging-rules/{rule_id}")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert data["data"]["id"] == rule_id
    assert data["data"]["lead_init_delay_h"] == payload["lead_init_delay_h"]


@pytest.mark.asyncio
async def test_get_messaging_rule_not_found(authenticated_client: AsyncClient):
    """Test getting non-existent messaging rule"""
    fake_id = "123e4567-e89b-12d3-a456-426614174000"
    response = await authenticated_client.get(f"/api/settings/messaging-rules/{fake_id}")
    assert response.status_code == status.HTTP_404_NOT_FOUND
    data = response.json()
    assert data["success"] is False


@pytest.mark.asyncio
async def test_get_messaging_rule_invalid_id(authenticated_client: AsyncClient):
    """Test getting messaging rule with invalid ID format"""
    response = await authenticated_client.get("/api/settings/messaging-rules/invalid-id")
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    data = response.json()
    assert data["success"] is False


@pytest.mark.asyncio
async def test_get_active_messaging_rule_success(authenticated_client: AsyncClient):
    """Test getting the active messaging rule"""
    payload = {
        "lead_init_delay_h": 2,
        "no_response_delay_h": 24,
        "max_followups": 3
    }
    create_resp = await authenticated_client.post("/api/settings/messaging-rules", json=payload)
    rule_id = create_resp.json()["data"]["id"]
    
    response = await authenticated_client.get("/api/settings/messaging-rules/active")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert data["data"]["id"] == rule_id
    assert data["data"]["is_active"] is True


@pytest.mark.asyncio
async def test_get_active_messaging_rule_not_found(authenticated_client: AsyncClient):
    """Test getting active messaging rule when none exists"""
    # First, ensure no active rules by creating and deleting one
    payload = {
        "lead_init_delay_h": 1,
        "no_response_delay_h": 12,
        "max_followups": 2
    }
    create_resp = await authenticated_client.post("/api/settings/messaging-rules", json=payload)
    rule_id = create_resp.json()["data"]["id"]
    await authenticated_client.delete(f"/api/settings/messaging-rules/{rule_id}")
    
    response = await authenticated_client.get("/api/settings/messaging-rules/active")
    assert response.status_code == status.HTTP_404_NOT_FOUND
    data = response.json()
    assert data["success"] is False


@pytest.mark.asyncio
async def test_list_messaging_rules_success(authenticated_client: AsyncClient):
    """Test successful messaging rule listing"""
    # Create test rules
    rules = [
        {
            "lead_init_delay_h": 1,
            "no_response_delay_h": 12,
            "max_followups": 2
        },
        {
            "lead_init_delay_h": 4,
            "no_response_delay_h": 48,
            "max_followups": 5
        }
    ]
    
    for rule in rules:
        await authenticated_client.post("/api/settings/messaging-rules", json=rule)
    
    response = await authenticated_client.get("/api/settings/messaging-rules")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert len(data["data"]["items"]) >= 1  # At least one active rule
    assert data["data"]["total_count"] >= 1


@pytest.mark.asyncio
async def test_list_messaging_rules_include_inactive(authenticated_client: AsyncClient):
    """Test messaging rule listing including inactive rules"""
    # Create multiple rules (only last one will be active)
    rules = [
        {
            "lead_init_delay_h": 1,
            "no_response_delay_h": 12,
            "max_followups": 2
        },
        {
            "lead_init_delay_h": 2,
            "no_response_delay_h": 24,
            "max_followups": 3
        }
    ]
    
    for rule in rules:
        await authenticated_client.post("/api/settings/messaging-rules", json=rule)
    
    # Test with include_inactive=true
    response = await authenticated_client.get("/api/settings/messaging-rules?include_inactive=true")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert len(data["data"]["items"]) >= 2  # Should include inactive rules


@pytest.mark.asyncio
async def test_list_messaging_rules_pagination(authenticated_client: AsyncClient):
    """Test messaging rule listing with pagination"""
    response = await authenticated_client.get("/api/settings/messaging-rules?skip=0&limit=5")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert len(data["data"]["items"]) <= 5


@pytest.mark.asyncio
async def test_update_messaging_rule_success(authenticated_client: AsyncClient):
    """Test successful messaging rule update"""
    # Create rule
    payload = {
        "lead_init_delay_h": 2,
        "no_response_delay_h": 24,
        "max_followups": 3
    }
    create_resp = await authenticated_client.post("/api/settings/messaging-rules", json=payload)
    rule_id = create_resp.json()["data"]["id"]
    
    # Update rule
    update_payload = {
        "lead_init_delay_h": 4,
        "max_followups": 5,
        "is_active": True
    }
    response = await authenticated_client.put(f"/api/settings/messaging-rules/{rule_id}", json=update_payload)
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert data["data"]["lead_init_delay_h"] == update_payload["lead_init_delay_h"]
    assert data["data"]["max_followups"] == update_payload["max_followups"]


@pytest.mark.asyncio
async def test_update_messaging_rule_activate_deactivates_others(authenticated_client: AsyncClient):
    """Test that activating a rule deactivates others"""
    # Create two rules
    payload1 = {
        "lead_init_delay_h": 1,
        "no_response_delay_h": 12,
        "max_followups": 2
    }
    response1 = await authenticated_client.post("/api/settings/messaging-rules", json=payload1)
    rule1_id = response1.json()["data"]["id"]
    
    payload2 = {
        "lead_init_delay_h": 2,
        "no_response_delay_h": 24,
        "max_followups": 3
    }
    response2 = await authenticated_client.post("/api/settings/messaging-rules", json=payload2)
    rule2_id = response2.json()["data"]["id"]
    
    # Activate the first rule (should deactivate second)
    update_payload = {"is_active": True}
    response = await authenticated_client.put(f"/api/settings/messaging-rules/{rule1_id}", json=update_payload)
    assert response.status_code == status.HTTP_200_OK
    
    # Check that first rule is active
    active_response = await authenticated_client.get("/api/settings/messaging-rules/active")
    assert active_response.status_code == status.HTTP_200_OK
    active_data = active_response.json()
    assert active_data["data"]["id"] == rule1_id


@pytest.mark.asyncio
async def test_update_messaging_rule_not_found(authenticated_client: AsyncClient):
    """Test updating non-existent messaging rule"""
    fake_id = "123e4567-e89b-12d3-a456-426614174000"
    update_payload = {"lead_init_delay_h": 5}
    response = await authenticated_client.put(f"/api/settings/messaging-rules/{fake_id}", json=update_payload)
    assert response.status_code == status.HTTP_404_NOT_FOUND
    data = response.json()
    assert data["success"] is False


@pytest.mark.asyncio
async def test_update_messaging_rule_invalid_constraints(authenticated_client: AsyncClient):
    """Test updating messaging rule with invalid constraints"""
    # Create rule
    payload = {
        "lead_init_delay_h": 2,
        "no_response_delay_h": 24,
        "max_followups": 3
    }
    create_resp = await authenticated_client.post("/api/settings/messaging-rules", json=payload)
    rule_id = create_resp.json()["data"]["id"]
    
    # Try to update with invalid values
    invalid_update = {"no_response_delay_h": 0}  # Must be > 0
    response = await authenticated_client.put(f"/api/settings/messaging-rules/{rule_id}", json=invalid_update)
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.asyncio
async def test_delete_messaging_rule_success(authenticated_client: AsyncClient):
    """Test successful messaging rule deletion"""
    # Create rule
    payload = {
        "lead_init_delay_h": 2,
        "no_response_delay_h": 24,
        "max_followups": 3
    }
    create_resp = await authenticated_client.post("/api/settings/messaging-rules", json=payload)
    rule_id = create_resp.json()["data"]["id"]
    
    # Delete rule
    response = await authenticated_client.delete(f"/api/settings/messaging-rules/{rule_id}")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert data["data"]["deleted"] is True
    
    # Verify rule is soft deleted
    get_response = await authenticated_client.get(f"/api/settings/messaging-rules/{rule_id}")
    assert get_response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.asyncio
async def test_delete_messaging_rule_not_found(authenticated_client: AsyncClient):
    """Test deleting non-existent messaging rule"""
    fake_id = "123e4567-e89b-12d3-a456-426614174000"
    response = await authenticated_client.delete(f"/api/settings/messaging-rules/{fake_id}")
    assert response.status_code == status.HTTP_404_NOT_FOUND
    data = response.json()
    assert data["success"] is False


@pytest.mark.asyncio
async def test_messaging_rule_endpoints_require_authentication():
    """Test that messaging rule endpoints require authentication"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        # Test all endpoints without authentication
        endpoints = [
            ("POST", "/api/settings/messaging-rules", {"lead_init_delay_h": 2, "no_response_delay_h": 24, "max_followups": 3}),
            ("GET", "/api/settings/messaging-rules", None),
            ("GET", "/api/settings/messaging-rules/active", None),
            ("GET", "/api/settings/messaging-rules/123e4567-e89b-12d3-a456-426614174000", None),
            ("PUT", "/api/settings/messaging-rules/123e4567-e89b-12d3-a456-426614174000", {"lead_init_delay_h": 5}),
            ("DELETE", "/api/settings/messaging-rules/123e4567-e89b-12d3-a456-426614174000", None),
        ]
        
        for method, url, payload in endpoints:
            if method == "POST":
                response = await client.post(url, json=payload)
            elif method == "PUT":
                response = await client.put(url, json=payload)
            elif method == "DELETE":
                response = await client.delete(url)
            else:
                response = await client.get(url)
            
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
