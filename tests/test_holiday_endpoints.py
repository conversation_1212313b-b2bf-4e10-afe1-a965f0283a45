"""
Test Holiday Endpoints
Comprehensive tests for holiday management API
"""

import pytest
from httpx import AsyncClient
from fastapi import status
from datetime import date, time
from app.main import app


@pytest.mark.asyncio
async def test_create_holiday_success(authenticated_client: AsyncClient):
    """Test successful holiday creation"""
    payload = {
        "holiday_type": "PREDEFINED",
        "date": "2024-12-25",
        "all_day": True,
        "start_time": None,
        "end_time": None,
        "description": "Christmas Day"
    }
    response = await authenticated_client.post("/api/settings/holidays", json=payload)
    assert response.status_code == status.HTTP_201_CREATED
    data = response.json()
    assert data["success"] is True
    assert data["data"]["holiday_type"] == payload["holiday_type"]
    assert data["data"]["date"] == payload["date"]
    assert data["data"]["all_day"] is True
    assert data["data"]["description"] == payload["description"]


@pytest.mark.asyncio
async def test_create_holiday_partial_day(authenticated_client: AsyncClient):
    """Test creating a partial day holiday"""
    payload = {
        "holiday_type": "PERSONAL",
        "date": "2024-07-04",
        "all_day": False,
        "start_time": "09:00:00",
        "end_time": "17:00:00",
        "description": "Independence Day - Half day"
    }
    response = await authenticated_client.post("/api/settings/holidays", json=payload)
    assert response.status_code == status.HTTP_201_CREATED
    data = response.json()
    assert data["success"] is True
    assert data["data"]["all_day"] is False
    assert data["data"]["start_time"] == payload["start_time"]
    assert data["data"]["end_time"] == payload["end_time"]


@pytest.mark.asyncio
async def test_create_holiday_duplicate(authenticated_client: AsyncClient):
    """Test creating duplicate holiday"""
    payload = {
        "holiday_type": "PREDEFINED",
        "date": "2024-01-01",
        "all_day": True,
        "description": "New Year's Day"
    }
    # First create
    await authenticated_client.post("/api/settings/holidays", json=payload)
    # Duplicate create
    response = await authenticated_client.post("/api/settings/holidays", json=payload)
    assert response.status_code == status.HTTP_409_CONFLICT
    data = response.json()
    assert data["success"] is False
    assert "already exists" in data["message"]["description"]


@pytest.mark.asyncio
async def test_create_holiday_invalid_time_constraint(authenticated_client: AsyncClient):
    """Test creating holiday with invalid time constraint"""
    payload = {
        "holiday_type": "PERSONAL",
        "date": "2024-07-04",
        "all_day": False,
        "start_time": None,  # Missing required time
        "end_time": "17:00:00",
        "description": "Invalid holiday"
    }
    response = await authenticated_client.post("/api/settings/holidays", json=payload)
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.asyncio
async def test_create_holiday_invalid_type(authenticated_client: AsyncClient):
    """Test creating holiday with invalid type"""
    payload = {
        "holiday_type": "INVALID_TYPE",
        "date": "2024-07-04",
        "all_day": True,
        "description": "Invalid type holiday"
    }
    response = await authenticated_client.post("/api/settings/holidays", json=payload)
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.asyncio
async def test_get_holiday_success(authenticated_client: AsyncClient):
    """Test successful holiday retrieval"""
    payload = {
        "holiday_type": "PREDEFINED",
        "date": "2024-03-17",
        "all_day": True,
        "description": "St. Patrick's Day"
    }
    create_resp = await authenticated_client.post("/api/settings/holidays", json=payload)
    holiday_id = create_resp.json()["data"]["id"]
    
    response = await authenticated_client.get(f"/api/settings/holidays/{holiday_id}")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert data["data"]["id"] == holiday_id
    assert data["data"]["description"] == payload["description"]


@pytest.mark.asyncio
async def test_get_holiday_not_found(authenticated_client: AsyncClient):
    """Test getting non-existent holiday"""
    fake_id = "123e4567-e89b-12d3-a456-426614174000"
    response = await authenticated_client.get(f"/api/settings/holidays/{fake_id}")
    assert response.status_code == status.HTTP_404_NOT_FOUND
    data = response.json()
    assert data["success"] is False


@pytest.mark.asyncio
async def test_get_holiday_invalid_id(authenticated_client: AsyncClient):
    """Test getting holiday with invalid ID format"""
    response = await authenticated_client.get("/api/settings/holidays/invalid-id")
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    data = response.json()
    assert data["success"] is False


@pytest.mark.asyncio
async def test_list_holidays_success(authenticated_client: AsyncClient):
    """Test successful holiday listing"""
    # Create test holidays
    holidays = [
        {
            "holiday_type": "PREDEFINED",
            "date": "2024-12-25",
            "all_day": True,
            "description": "Christmas"
        },
        {
            "holiday_type": "PERSONAL",
            "date": "2024-07-04",
            "all_day": False,
            "start_time": "09:00:00",
            "end_time": "17:00:00",
            "description": "Independence Day"
        }
    ]
    
    for holiday in holidays:
        await authenticated_client.post("/api/settings/holidays", json=holiday)
    
    response = await authenticated_client.get("/api/settings/holidays")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert len(data["data"]["items"]) >= 2
    assert data["data"]["total_count"] >= 2


@pytest.mark.asyncio
async def test_list_holidays_with_filters(authenticated_client: AsyncClient):
    """Test holiday listing with filters"""
    # Create test holiday
    payload = {
        "holiday_type": "PREDEFINED",
        "date": "2024-11-28",
        "all_day": True,
        "description": "Thanksgiving"
    }
    await authenticated_client.post("/api/settings/holidays", json=payload)
    
    # Test filter by type
    response = await authenticated_client.get("/api/settings/holidays?holiday_type=PREDEFINED")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    
    # Test search
    response = await authenticated_client.get("/api/settings/holidays?search=Thanksgiving")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True


@pytest.mark.asyncio
async def test_list_holidays_pagination(authenticated_client: AsyncClient):
    """Test holiday listing with pagination"""
    response = await authenticated_client.get("/api/settings/holidays?skip=0&limit=5")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert len(data["data"]["items"]) <= 5


@pytest.mark.asyncio
async def test_list_holidays_invalid_date_range(authenticated_client: AsyncClient):
    """Test holiday listing with invalid date range"""
    response = await authenticated_client.get(
        "/api/settings/holidays?start_date=2024-12-31&end_date=2024-01-01"
    )
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    data = response.json()
    assert data["success"] is False


@pytest.mark.asyncio
async def test_update_holiday_success(authenticated_client: AsyncClient):
    """Test successful holiday update"""
    # Create holiday
    payload = {
        "holiday_type": "PREDEFINED",
        "date": "2024-10-31",
        "all_day": True,
        "description": "Halloween"
    }
    create_resp = await authenticated_client.post("/api/settings/holidays", json=payload)
    holiday_id = create_resp.json()["data"]["id"]
    
    # Update holiday
    update_payload = {
        "description": "Halloween - Updated",
        "is_active": True
    }
    response = await authenticated_client.put(f"/api/settings/holidays/{holiday_id}", json=update_payload)
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert data["data"]["description"] == update_payload["description"]


@pytest.mark.asyncio
async def test_update_holiday_not_found(authenticated_client: AsyncClient):
    """Test updating non-existent holiday"""
    fake_id = "123e4567-e89b-12d3-a456-426614174000"
    update_payload = {"description": "Updated"}
    response = await authenticated_client.put(f"/api/settings/holidays/{fake_id}", json=update_payload)
    assert response.status_code == status.HTTP_404_NOT_FOUND
    data = response.json()
    assert data["success"] is False


@pytest.mark.asyncio
async def test_delete_holiday_success(authenticated_client: AsyncClient):
    """Test successful holiday deletion"""
    # Create holiday
    payload = {
        "holiday_type": "PERSONAL",
        "date": "2024-02-14",
        "all_day": True,
        "description": "Valentine's Day"
    }
    create_resp = await authenticated_client.post("/api/settings/holidays", json=payload)
    holiday_id = create_resp.json()["data"]["id"]
    
    # Delete holiday
    response = await authenticated_client.delete(f"/api/settings/holidays/{holiday_id}")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["success"] is True
    assert data["data"]["deleted"] is True
    
    # Verify holiday is soft deleted
    get_response = await authenticated_client.get(f"/api/settings/holidays/{holiday_id}")
    assert get_response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.asyncio
async def test_delete_holiday_not_found(authenticated_client: AsyncClient):
    """Test deleting non-existent holiday"""
    fake_id = "123e4567-e89b-12d3-a456-426614174000"
    response = await authenticated_client.delete(f"/api/settings/holidays/{fake_id}")
    assert response.status_code == status.HTTP_404_NOT_FOUND
    data = response.json()
    assert data["success"] is False


@pytest.mark.asyncio
async def test_holiday_endpoints_require_authentication():
    """Test that holiday endpoints require authentication"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        # Test all endpoints without authentication
        endpoints = [
            ("POST", "/api/settings/holidays", {"holiday_type": "PREDEFINED", "date": "2024-01-01", "all_day": True}),
            ("GET", "/api/settings/holidays", None),
            ("GET", "/api/settings/holidays/123e4567-e89b-12d3-a456-426614174000", None),
            ("PUT", "/api/settings/holidays/123e4567-e89b-12d3-a456-426614174000", {"description": "Updated"}),
            ("DELETE", "/api/settings/holidays/123e4567-e89b-12d3-a456-426614174000", None),
        ]
        
        for method, url, payload in endpoints:
            if method == "POST":
                response = await client.post(url, json=payload)
            elif method == "PUT":
                response = await client.put(url, json=payload)
            elif method == "DELETE":
                response = await client.delete(url)
            else:
                response = await client.get(url)
            
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
