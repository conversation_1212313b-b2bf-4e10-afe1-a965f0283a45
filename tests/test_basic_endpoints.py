"""
Basic endpoint tests to verify all endpoints are accessible and working.
These tests focus on endpoint existence and basic functionality.
"""

import pytest
from fastapi.testclient import TestClient
from app.main import app


class TestBasicEndpoints:
    """Test basic endpoint functionality"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_root_endpoint(self):
        """Test API root endpoint"""
        response = self.client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
    
    def test_api_root_endpoint(self):
        """Test API v1 root endpoint"""
        response = self.client.get("/api/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
    
    def test_health_endpoint(self):
        """Test health check endpoint"""
        response = self.client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
    
    def test_docs_endpoints(self):
        """Test documentation endpoints"""
        # Test Swagger UI
        response = self.client.get("/docs")
        assert response.status_code == 200
        
        # Test ReDoc
        response = self.client.get("/redoc")
        assert response.status_code == 200
        
        # Test OpenAPI schema
        response = self.client.get("/openapi.json")
        assert response.status_code == 200
        schema = response.json()
        assert "openapi" in schema
        assert "paths" in schema


class TestAuthEndpoints:
    """Test authentication endpoints exist and respond"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_register_endpoint_exists(self):
        """Test register endpoint exists"""
        # Test with empty data to verify endpoint exists
        response = self.client.post("/api/auth/register", json={})
        # Should return 422 (validation error) not 404 (not found)
        assert response.status_code == 422
        
        # Verify it's a validation error, not endpoint not found
        data = response.json()
        assert "detail" in data  # FastAPI validation error format
    
    def test_login_endpoint_exists(self):
        """Test login endpoint exists"""
        # Test with empty data to verify endpoint exists
        response = self.client.post("/api/auth/login", json={})
        # Should return 422 (validation error) not 404 (not found)
        assert response.status_code == 422
        
        # Verify it's a validation error, not endpoint not found
        data = response.json()
        assert "detail" in data  # FastAPI validation error format
    
    def test_protected_endpoint_requires_auth(self):
        """Test protected endpoints require authentication"""
        # Test /me endpoint without authentication
        response = self.client.get("/api/auth/me")
        # Should return 401 (unauthorized) or 404 if endpoint doesn't exist
        assert response.status_code in [401, 404]


class TestCategoryEndpoints:
    """Test category endpoints exist and respond"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_list_categories_endpoint_exists(self):
        """Test list categories endpoint exists"""
        response = self.client.get("/api/categories")
        # Should not return 404 (endpoint should exist)
        # May return 401 if authentication required
        assert response.status_code != 404
    
    def test_create_category_endpoint_exists(self):
        """Test create category endpoint exists"""
        response = self.client.post("/api/categories", json={})
        # Should return 422 (validation error) or 401 (auth required), not 404
        assert response.status_code in [422, 401]
    
    def test_get_category_endpoint_exists(self):
        """Test get category by ID endpoint exists"""
        test_id = "123e4567-e89b-12d3-a456-426614174000"
        response = self.client.get(f"/api/categories/{test_id}")
        # Should not return 404 for endpoint (may return 404 for resource)
        # Check if it's an endpoint 404 vs resource 404
        if response.status_code == 404:
            # If it's a resource not found, the response should mention the resource
            assert "not found" in response.text.lower() or "category" in response.text.lower()


class TestSubcategoryEndpoints:
    """Test subcategory endpoints exist and respond"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_list_subcategories_endpoint_exists(self):
        """Test list subcategories endpoint exists"""
        test_category_id = "123e4567-e89b-12d3-a456-426614174000"
        response = self.client.get(f"/api/categories/{test_category_id}/subcategories")
        # Should not return 404 (endpoint should exist)
        assert response.status_code != 404
    
    def test_create_subcategory_endpoint_exists(self):
        """Test create subcategory endpoint exists"""
        test_category_id = "123e4567-e89b-12d3-a456-426614174000"
        response = self.client.post(f"/api/categories/{test_category_id}/subcategories", json={})
        # Should return 422 (validation error) or 401 (auth required), not 404
        assert response.status_code in [422, 401]


class TestFranchisorEndpoints:
    """Test franchisor endpoints exist and respond"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_list_franchisors_endpoint_exists(self):
        """Test list franchisors endpoint exists"""
        response = self.client.get("/api/franchisors")
        # Should not return 404 (endpoint should exist)
        assert response.status_code != 404
    
    def test_create_franchisor_endpoint_exists(self):
        """Test create franchisor endpoint exists"""
        response = self.client.post("/api/franchisors", json={})
        # Should return 422 (validation error) or 401 (auth required), not 404
        assert response.status_code in [422, 401]
    
    def test_get_franchisor_endpoint_exists(self):
        """Test get franchisor by ID endpoint exists"""
        test_id = "frc_123456789"
        response = self.client.get(f"/api/franchisors/{test_id}")
        # Should not return 404 for endpoint (may return 404 for resource)
        if response.status_code == 404:
            # If it's a resource not found, the response should mention the resource
            assert "not found" in response.text.lower() or "franchisor" in response.text.lower()


class TestErrorHandling:
    """Test error handling"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_404_for_nonexistent_endpoint(self):
        """Test 404 for truly nonexistent endpoints"""
        response = self.client.get("/api/nonexistent-endpoint-12345")
        assert response.status_code == 404
    
    def test_invalid_json_handling(self):
        """Test handling of invalid JSON"""
        response = self.client.post(
            "/api/categories",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        # Should handle gracefully (422, 400, or 401)
        assert response.status_code in [422, 400, 401]
    
    def test_method_not_allowed(self):
        """Test method not allowed handling"""
        # Try PATCH on an endpoint that likely doesn't support it
        response = self.client.patch("/api/categories")
        # Should return 405 (Method Not Allowed) or other appropriate error
        assert response.status_code in [405, 404, 401]


class TestSecurity:
    """Test basic security measures"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_sql_injection_protection(self):
        """Test protection against basic SQL injection attempts"""
        malicious_inputs = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "admin'--",
            "1; DELETE FROM users; --"
        ]
        
        for malicious_input in malicious_inputs:
            # Test in URL path
            response = self.client.get(f"/api/categories/{malicious_input}")
            # Should handle gracefully, not crash
            assert response.status_code in [400, 404, 422, 401]
            
            # Test in request body
            response = self.client.post("/api/categories", json={"name": malicious_input})
            # Should handle gracefully
            assert response.status_code in [400, 422, 401]
    
    def test_xss_protection(self):
        """Test protection against XSS attempts"""
        xss_payloads = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>"
        ]
        
        for payload in xss_payloads:
            response = self.client.post("/api/categories", json={"name": payload})
            # Should handle gracefully
            assert response.status_code in [400, 422, 401]
    
    def test_large_payload_handling(self):
        """Test handling of large payloads"""
        large_string = "x" * 100000  # 100KB string
        response = self.client.post("/api/categories", json={"name": large_string})
        # Should handle gracefully
        assert response.status_code in [400, 413, 422, 401]  # 413 = Payload Too Large


if __name__ == "__main__":
    # Allow running this file directly
    import subprocess
    import sys
    
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        __file__, 
        "-v"
    ])
    sys.exit(result.returncode)
