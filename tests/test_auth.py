"""Test authentication endpoints"""
import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import delete
from app.main import app
from app.models.user import User
from app.core.database.connection import get_db
from datetime import datetime, timedelta

class CustomTestClient(TestClient):
    def request(self, *args, **kwargs):
        headers = kwargs.pop('headers', {}) or {}
        headers.setdefault('host', 'localhost')
        kwargs['headers'] = headers
        return super().request(*args, **kwargs)

@pytest.fixture
def client(test_db):
    # Override get_db before app starts
    async def _override_get_db():
        yield test_db
    app.dependency_overrides[get_db] = _override_get_db
    with CustomTestClient(app) as c:
        yield c
    app.dependency_overrides.clear()

@pytest_asyncio.fixture
async def test_user(test_db: AsyncSession):
    # Delete any existing user with the same email
    await test_db.execute(delete(User).where(User.email == "<EMAIL>"))
    await test_db.commit()
    user = User(
        email="<EMAIL>",
        password_hash="$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBAQHxJ5QJ5KQy",  # password123
        is_active=True
    )
    test_db.add(user)
    await test_db.commit()
    await test_db.refresh(user)
    return user

def test_register(client: TestClient):
    """Test user registration"""
    response = client.post(
        "/api/v1/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "password123",
            "confirm_password": "password123"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["data"]["user"]["email"] == "<EMAIL>"
    assert "id" in data["data"]["user"]
    assert "created_at" in data["data"]["user"]
    assert "updated_at" in data["data"]["user"]

def test_register_existing_email(client: TestClient, test_user: User):
    """Test registration with existing email"""
    response = client.post(
        "/api/v1/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "password123",
            "confirm_password": "password123"
        }
    )
    assert response.status_code == 409
    data = response.json()
    assert data["detail"] == "Email already registered"

def test_login(client: TestClient, test_user: User):
    """Test user login without remember me"""
    response = client.post(
        "/api/v1/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "password123",
            "remember_me": False
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "access_token" in data["data"]
    assert data["data"]["user"]["email"] == "<EMAIL>"
    assert data["data"]["token_type"] == "bearer"
    assert data["data"]["expires_in"] <= 900  # 15 minutes in seconds

def test_login_with_remember_me(client: TestClient, test_user: User):
    """Test user login with remember me"""
    response = client.post(
        "/api/v1/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "password123",
            "remember_me": True
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "access_token" in data["data"]
    assert data["data"]["user"]["email"] == "<EMAIL>"
    assert data["data"]["token_type"] == "bearer"
    assert data["data"]["expires_in"] >= 2592000  # 30 days in seconds

def test_login_invalid_credentials(client: TestClient):
    """Test login with invalid credentials"""
    response = client.post(
        "/api/v1/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "wrongpass",
            "remember_me": False
        }
    )
    assert response.status_code == 401
    data = response.json()
    assert data["detail"] == "Invalid email or password"

def test_get_current_user(client: TestClient, test_user: User):
    """Test get current user info"""
    # First login to get token
    login_response = client.post(
        "/api/v1/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "password123",
            "remember_me": False
        }
    )
    token = login_response.json()["data"]["access_token"]

    # Get user info
    response = client.get(
        "/api/v1/auth/me",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["data"]["email"] == "<EMAIL>"
    assert "id" in data["data"]
    assert "created_at" in data["data"]
    assert "updated_at" in data["data"]

def test_get_current_user_unauthorized(client: TestClient):
    """Test get current user without token"""
    response = client.get("/api/v1/auth/me")
    assert response.status_code == 401
    data = response.json()
    assert data["detail"] == "Not authenticated"

def test_logout(client: TestClient, test_user: User):
    """Test user logout"""
    # First login to get token
    login_response = client.post(
        "/api/v1/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "password123",
            "remember_me": False
        }
    )
    token = login_response.json()["data"]["access_token"]

    # Logout
    response = client.post(
        "/api/v1/auth/logout",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["message"]["title"] == "Logout Successful"
    assert data["message"]["description"] == "User logged out successfully" 