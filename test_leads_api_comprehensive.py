#!/usr/bin/env python3
"""
Comprehensive Lead API Testing Script
Tests all lead endpoints and identifies issues
"""

import requests
import json
import csv
import io
import uuid
from typing import Dict, Any, List
from datetime import datetime

class LeadAPITester:
    def __init__(self, base_url: str = "http://localhost:8000", token: str = None):
        self.base_url = base_url
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}" if token else None
        }
        self.test_results = []
        self.created_leads = []  # Track created leads for cleanup
        
    def log_test(self, test_name: str, success: bool, details: str = "", response_data: Any = None):
        """Log test results"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "timestamp": datetime.now().isoformat(),
            "response_data": response_data
        }
        self.test_results.append(result)
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {details}")
        
    def test_list_leads_basic(self):
        """Test basic lead listing"""
        try:
            response = requests.get(f"{self.base_url}/api/leads/", headers=self.headers)
            success = response.status_code == 200
            data = response.json() if success else None
            
            if success and data:
                success = data.get("success", False) and "data" in data
                details = f"Status: {response.status_code}, Found {len(data.get('data', {}).get('items', []))} leads"
            else:
                details = f"Status: {response.status_code}, Response: {response.text[:200]}"
                
            self.log_test("List Leads Basic", success, details, data)
            return success
        except Exception as e:
            self.log_test("List Leads Basic", False, f"Exception: {str(e)}")
            return False
    
    def test_create_lead(self):
        """Test lead creation"""
        lead_data = {
            "full_name": "Test Lead " + str(uuid.uuid4())[:8],
            "contact_number": f"+1{str(uuid.uuid4().int)[:10]}",
            "email": f"test{str(uuid.uuid4())[:8]}@example.com",
            "location": "Test City",
            "lead_source": "API Test",
            "franchise_preference": "Food",
            "budget_preference": 75000.00,
            "qualification_status": "new"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/api/leads/",
                headers=self.headers,
                json=lead_data
            )
            success = response.status_code in [200, 201]  # Accept both 200 and 201 for creation
            data = response.json() if success else None

            if success and data:
                success = data.get("success", False) and "data" in data
                if success:
                    lead_id = data["data"].get("id")
                    if lead_id:
                        self.created_leads.append(lead_id)
                    details = f"Created lead with ID: {lead_id}"
                else:
                    details = f"Response success=False: {data}"
            else:
                details = f"Status: {response.status_code}, Response: {response.text[:200]}"
                
            self.log_test("Create Lead", success, details, data)
            return success, data.get("data", {}).get("id") if success and data else None
        except Exception as e:
            self.log_test("Create Lead", False, f"Exception: {str(e)}")
            return False, None
    
    def test_get_lead_by_id(self, lead_id: str):
        """Test getting lead by ID"""
        try:
            response = requests.get(f"{self.base_url}/api/leads/{lead_id}", headers=self.headers)
            success = response.status_code == 200
            data = response.json() if success else None
            
            if success and data:
                success = data.get("success", False) and "data" in data
                details = f"Retrieved lead: {data['data'].get('full_name', 'Unknown')}"
            else:
                details = f"Status: {response.status_code}, Response: {response.text[:200]}"
                
            self.log_test("Get Lead By ID", success, details, data)
            return success
        except Exception as e:
            self.log_test("Get Lead By ID", False, f"Exception: {str(e)}")
            return False
    
    def test_update_lead(self, lead_id: str):
        """Test lead update"""
        update_data = {
            "full_name": "Updated Test Lead",
            "qualification_status": "qualified"
        }
        
        try:
            response = requests.put(
                f"{self.base_url}/api/leads/{lead_id}",
                headers=self.headers,
                json=update_data
            )
            success = response.status_code == 200
            data = response.json() if success else None
            
            if success and data:
                success = data.get("success", False) and "data" in data
                details = f"Updated lead name to: {data['data'].get('full_name', 'Unknown')}"
            else:
                details = f"Status: {response.status_code}, Response: {response.text[:200]}"
                
            self.log_test("Update Lead", success, details, data)
            return success
        except Exception as e:
            self.log_test("Update Lead", False, f"Exception: {str(e)}")
            return False
    
    def test_search_leads(self):
        """Test lead search functionality"""
        try:
            response = requests.get(
                f"{self.base_url}/api/leads/?search=Test",
                headers=self.headers
            )
            success = response.status_code == 200
            data = response.json() if success else None
            
            if success and data:
                success = data.get("success", False) and "data" in data
                items = data.get("data", {}).get("items", [])
                details = f"Search returned {len(items)} results"
            else:
                details = f"Status: {response.status_code}, Response: {response.text[:200]}"
                
            self.log_test("Search Leads", success, details, data)
            return success
        except Exception as e:
            self.log_test("Search Leads", False, f"Exception: {str(e)}")
            return False
    
    def test_filter_leads(self):
        """Test lead filtering"""
        try:
            response = requests.get(
                f"{self.base_url}/api/leads/?status=new&lead_source=API Test",
                headers=self.headers
            )
            success = response.status_code == 200
            data = response.json() if success else None
            
            if success and data:
                success = data.get("success", False) and "data" in data
                items = data.get("data", {}).get("items", [])
                details = f"Filter returned {len(items)} results"
            else:
                details = f"Status: {response.status_code}, Response: {response.text[:200]}"
                
            self.log_test("Filter Leads", success, details, data)
            return success
        except Exception as e:
            self.log_test("Filter Leads", False, f"Exception: {str(e)}")
            return False
    
    def test_sort_leads(self):
        """Test lead sorting"""
        try:
            response = requests.get(
                f"{self.base_url}/api/leads/?sort=name_asc",
                headers=self.headers
            )
            success = response.status_code == 200
            data = response.json() if success else None
            
            if success and data:
                success = data.get("success", False) and "data" in data
                items = data.get("data", {}).get("items", [])
                details = f"Sort returned {len(items)} results"
            else:
                details = f"Status: {response.status_code}, Response: {response.text[:200]}"
                
            self.log_test("Sort Leads", success, details, data)
            return success
        except Exception as e:
            self.log_test("Sort Leads", False, f"Exception: {str(e)}")
            return False
    
    def create_test_csv(self) -> bytes:
        """Create test CSV content"""
        csv_data = [
            {
                'full_name': 'CSV Test Lead 1',
                'contact_number': '+1111111111',
                'email': '<EMAIL>',
                'location': 'CSV City 1',
                'lead_source': 'CSV Import',
                'franchise_preference': 'Food',
                'budget_preference': '50000',
                'qualification_status': 'new',
                'is_active': 'true',
                'is_deleted': 'false'
            },
            {
                'full_name': 'CSV Test Lead 2',
                'contact_number': '+2222222222',
                'email': '<EMAIL>',
                'location': 'CSV City 2',
                'lead_source': 'CSV Import',
                'franchise_preference': 'Retail',
                'budget_preference': '75000',
                'qualification_status': 'new',
                'is_active': '1',
                'is_deleted': '0'
            }
        ]
        
        output = io.StringIO()
        fieldnames = ['full_name', 'contact_number', 'email', 'location', 
                     'lead_source', 'franchise_preference', 'budget_preference',
                     'qualification_status', 'is_active', 'is_deleted']
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        for row in csv_data:
            writer.writerow(row)
        
        return output.getvalue().encode('utf-8')
    
    def test_bulk_upload(self):
        """Test CSV bulk upload"""
        try:
            csv_content = self.create_test_csv()
            files = {'file': ('test_leads.csv', csv_content, 'text/csv')}
            
            # Remove Content-Type for file upload
            headers = {k: v for k, v in self.headers.items() if k != 'Content-Type'}
            
            response = requests.post(
                f"{self.base_url}/api/leads/bulk-upload",
                headers=headers,
                files=files
            )
            success = response.status_code == 200
            data = response.json() if success else None
            
            if success and data:
                success = data.get("success", False) and "data" in data
                upload_data = data.get("data", {})
                details = f"Processed: {upload_data.get('total_processed', 0)}, " \
                         f"Imported: {upload_data.get('successful_imports', 0)}, " \
                         f"Duplicates: {upload_data.get('duplicates_found', 0)}, " \
                         f"Errors: {upload_data.get('errors_found', 0)}"
            else:
                details = f"Status: {response.status_code}, Response: {response.text[:200]}"
                
            self.log_test("Bulk Upload", success, details, data)
            return success
        except Exception as e:
            self.log_test("Bulk Upload", False, f"Exception: {str(e)}")
            return False
    
    def test_delete_lead(self, lead_id: str):
        """Test lead deletion"""
        try:
            response = requests.delete(f"{self.base_url}/api/leads/{lead_id}", headers=self.headers)
            success = response.status_code == 200
            data = response.json() if success else None
            
            if success and data:
                success = data.get("success", False)
                details = f"Deleted lead {lead_id}"
            else:
                details = f"Status: {response.status_code}, Response: {response.text[:200]}"
                
            self.log_test("Delete Lead", success, details, data)
            return success
        except Exception as e:
            self.log_test("Delete Lead", False, f"Exception: {str(e)}")
            return False
    
    def cleanup_created_leads(self):
        """Clean up leads created during testing"""
        print("\n🧹 Cleaning up created leads...")
        for lead_id in self.created_leads:
            try:
                self.test_delete_lead(lead_id)
            except:
                pass
    
    def run_all_tests(self):
        """Run all lead API tests"""
        print("🚀 Starting comprehensive Lead API testing...\n")
        
        # Test 1: List leads
        self.test_list_leads_basic()
        
        # Test 2: Create lead
        create_success, lead_id = self.test_create_lead()
        
        if create_success and lead_id:
            # Test 3: Get lead by ID
            self.test_get_lead_by_id(lead_id)
            
            # Test 4: Update lead
            self.test_update_lead(lead_id)
            
            # Test 5: Get updated lead
            self.test_get_lead_by_id(lead_id)
        
        # Test 6: Search functionality
        self.test_search_leads()
        
        # Test 7: Filter functionality
        self.test_filter_leads()
        
        # Test 8: Sort functionality
        self.test_sort_leads()
        
        # Test 9: Bulk upload
        self.test_bulk_upload()
        
        # Cleanup
        self.cleanup_created_leads()
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"\n📊 Test Summary:")
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test_name']}: {result['details']}")
        
        return failed_tests == 0


if __name__ == "__main__":
    # Use the token provided by the user
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************.0wgzTfyNPG4WPnAe5RvBUY4XQZPuUrcmhl4S-gglIrY"
    
    tester = LeadAPITester(token=token)
    success = tester.run_all_tests()
    
    if not success:
        exit(1)
