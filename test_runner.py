#!/usr/bin/env python3
"""
GrowthHive Endpoint Test Runner
===============================

This script runs comprehensive tests to verify all endpoints are working correctly.
It can be run multiple times and provides detailed reporting.

Usage:
    python test_runner.py                    # Run all tests
    python test_runner.py --quick            # Run quick tests only
    python test_runner.py --auth             # Run auth tests only
    python test_runner.py --categories       # Run category tests only
    python test_runner.py --franchisors      # Run franchisor tests only
    python test_runner.py --report           # Generate detailed report
"""

import argparse
import subprocess
import sys
import json
import time
from datetime import datetime
from pathlib import Path


class TestRunner:
    """Test runner for GrowthHive endpoints"""
    
    def __init__(self):
        self.results = {}
        self.start_time = None
        self.end_time = None
    
    def run_tests(self, test_pattern="tests/", verbose=True, quick=False):
        """Run tests with specified pattern"""
        self.start_time = time.time()
        
        cmd = [sys.executable, "-m", "pytest"]
        
        if quick:
            cmd.extend([
                "tests/test_comprehensive_endpoints.py::TestComprehensiveEndpoints",
                "-v", "--tb=short", "-x"  # Stop on first failure for quick tests
            ])
        else:
            cmd.extend([
                test_pattern,
                "-v", "--tb=short"
            ])
        
        if verbose:
            print(f"Running command: {' '.join(cmd)}")
            print("=" * 80)
        
        try:
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                cwd=Path(__file__).parent
            )
            
            self.end_time = time.time()
            
            self.results = {
                "success": result.returncode == 0,
                "return_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "duration": self.end_time - self.start_time,
                "timestamp": datetime.now().isoformat()
            }
            
            return self.results
            
        except Exception as e:
            self.end_time = time.time()
            self.results = {
                "success": False,
                "return_code": -1,
                "stdout": "",
                "stderr": str(e),
                "duration": self.end_time - self.start_time if self.start_time else 0,
                "timestamp": datetime.now().isoformat()
            }
            return self.results
    
    def run_specific_tests(self, test_type):
        """Run specific test categories"""
        test_patterns = {
            "auth": "tests/test_comprehensive_endpoints.py::TestAuthenticationEndpoints",
            "categories": "tests/test_comprehensive_endpoints.py::TestCategoryEndpoints",
            "subcategories": "tests/test_comprehensive_endpoints.py::TestSubcategoryEndpoints", 
            "franchisors": "tests/test_comprehensive_endpoints.py::TestFranchisorEndpoints",
            "security": "tests/test_comprehensive_endpoints.py::TestEndpointSecurity",
            "errors": "tests/test_comprehensive_endpoints.py::TestErrorHandling",
            "comprehensive": "tests/test_comprehensive_endpoints.py"
        }
        
        pattern = test_patterns.get(test_type, "tests/")
        return self.run_tests(pattern)
    
    def print_results(self, detailed=False):
        """Print test results"""
        if not self.results:
            print("No test results available")
            return
        
        print("\n" + "=" * 80)
        print("TEST RESULTS SUMMARY")
        print("=" * 80)
        
        status = "✅ PASSED" if self.results["success"] else "❌ FAILED"
        print(f"Status: {status}")
        print(f"Duration: {self.results['duration']:.2f} seconds")
        print(f"Return Code: {self.results['return_code']}")
        print(f"Timestamp: {self.results['timestamp']}")
        
        if self.results["stdout"]:
            print("\n" + "-" * 40)
            print("TEST OUTPUT:")
            print("-" * 40)
            print(self.results["stdout"])
        
        if self.results["stderr"]:
            print("\n" + "-" * 40)
            print("ERRORS/WARNINGS:")
            print("-" * 40)
            print(self.results["stderr"])
        
        if detailed and not self.results["success"]:
            print("\n" + "-" * 40)
            print("TROUBLESHOOTING TIPS:")
            print("-" * 40)
            self._print_troubleshooting_tips()
    
    def _print_troubleshooting_tips(self):
        """Print troubleshooting tips based on common errors"""
        stderr = self.results.get("stderr", "")
        stdout = self.results.get("stdout", "")
        
        tips = []
        
        if "database" in stderr.lower() or "connection" in stderr.lower():
            tips.append("• Check database connection and ensure PostgreSQL is running")
            tips.append("• Verify database credentials in environment variables")
        
        if "import" in stderr.lower():
            tips.append("• Check for missing dependencies: pip install -r requirements.txt")
            tips.append("• Verify Python path and module imports")
        
        if "authentication" in stderr.lower() or "401" in stderr:
            tips.append("• Check JWT secret key configuration")
            tips.append("• Verify authentication middleware setup")
        
        if "404" in stderr or "not found" in stderr.lower():
            tips.append("• Check API route definitions and URL patterns")
            tips.append("• Verify endpoint registration in main.py")
        
        if "500" in stderr or "internal server error" in stderr.lower():
            tips.append("• Check application logs for detailed error information")
            tips.append("• Verify all required environment variables are set")
        
        if not tips:
            tips.append("• Check the detailed error output above")
            tips.append("• Ensure all dependencies are installed")
            tips.append("• Verify environment configuration")
        
        for tip in tips:
            print(tip)
    
    def save_report(self, filename=None):
        """Save test results to a JSON report"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_report_{timestamp}.json"
        
        report_data = {
            "test_run": self.results,
            "system_info": {
                "python_version": sys.version,
                "platform": sys.platform,
                "cwd": str(Path.cwd())
            }
        }
        
        with open(filename, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\nTest report saved to: {filename}")
        return filename


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="GrowthHive Endpoint Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        "--quick", 
        action="store_true", 
        help="Run quick tests only (basic endpoint checks)"
    )
    
    parser.add_argument(
        "--auth", 
        action="store_true", 
        help="Run authentication tests only"
    )
    
    parser.add_argument(
        "--categories", 
        action="store_true", 
        help="Run category tests only"
    )
    
    parser.add_argument(
        "--subcategories", 
        action="store_true", 
        help="Run subcategory tests only"
    )
    
    parser.add_argument(
        "--franchisors", 
        action="store_true", 
        help="Run franchisor tests only"
    )
    
    parser.add_argument(
        "--security", 
        action="store_true", 
        help="Run security tests only"
    )
    
    parser.add_argument(
        "--errors", 
        action="store_true", 
        help="Run error handling tests only"
    )
    
    parser.add_argument(
        "--comprehensive", 
        action="store_true", 
        help="Run comprehensive endpoint tests only"
    )
    
    parser.add_argument(
        "--report", 
        action="store_true", 
        help="Generate detailed JSON report"
    )
    
    parser.add_argument(
        "--verbose", 
        action="store_true", 
        default=True,
        help="Verbose output (default: True)"
    )
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    print("🚀 GrowthHive Endpoint Test Runner")
    print("=" * 80)
    
    # Determine which tests to run
    if args.auth:
        print("Running authentication tests...")
        runner.run_specific_tests("auth")
    elif args.categories:
        print("Running category tests...")
        runner.run_specific_tests("categories")
    elif args.subcategories:
        print("Running subcategory tests...")
        runner.run_specific_tests("subcategories")
    elif args.franchisors:
        print("Running franchisor tests...")
        runner.run_specific_tests("franchisors")
    elif args.security:
        print("Running security tests...")
        runner.run_specific_tests("security")
    elif args.errors:
        print("Running error handling tests...")
        runner.run_specific_tests("errors")
    elif args.comprehensive:
        print("Running comprehensive tests...")
        runner.run_specific_tests("comprehensive")
    elif args.quick:
        print("Running quick tests...")
        runner.run_tests(quick=True)
    else:
        print("Running all tests...")
        runner.run_tests("tests/test_comprehensive_endpoints.py")
    
    # Print results
    runner.print_results(detailed=True)
    
    # Save report if requested
    if args.report:
        runner.save_report()
    
    # Exit with appropriate code
    sys.exit(0 if runner.results.get("success", False) else 1)


if __name__ == "__main__":
    main()
