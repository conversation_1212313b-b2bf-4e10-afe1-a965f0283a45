from app.repositories.base import BaseRepository
from app.models.subcategory import Subcategory
from app.schemas.subcategory import SubcategoryCreateRequest, SubcategoryUpdateRequest
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import Optional, Union
import uuid

class SubcategoryRepository(BaseRepository[Subcategory, SubcategoryCreateRequest, SubcategoryUpdateRequest]):
    """Repository for Subcategory data access."""
    def __init__(self, db: AsyncSession):
        super().__init__(Subcategory, db)

    async def get_by_name_and_category(self, name: str, category_id: Union[str, uuid.UUID]) -> Optional[Subcategory]:
        """Get subcategory by name and category UUID"""
        result = await self.db.execute(
            select(self.model).where(self.model.name == name, self.model.category_id == category_id)
        )
        return result.scalar_one_or_none()

    async def get_by_category(self, category_id: Union[str, uuid.UUID]) -> list[Subcategory]:
        """Get all subcategories for a specific category UUID"""
        result = await self.db.execute(
            select(self.model).where(self.model.category_id == category_id)
        )
        return result.scalars().all()