"""
Franchisor management endpoints with full Swagger documentation
"""

import base64
import csv
import io
from typing import Optional, Annotated
from fastapi import APIRouter, Depends, Query, Path, Header, UploadFile, File, Form, Request, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from app.schemas.franchisor import (
    FranchisorCreateRequest,
    FranchisorUpdateRequest,
    FranchisorResponse,
    FranchisorListResponse,
    FranchisorImportResponse,
    FranchisorDeleteResponse,
    FranchisorRegion,
    FranchisorSuccessResponse,
    FranchisorListSuccessResponse,
    FranchisorImportSuccessResponse,
    FranchisorDeleteSuccessResponse
)
from app.schemas.base_response import (
    SuccessResponse,
    ListResponse,
    COMMON_RESPONSES,
    ResponseMessage,
    PaginationInfo
)
from app.core.database.connection import get_db
from app.core.security.enhanced_auth_middleware import get_current_user, get_current_active_user, verify_admin_user
from app.core.responses import create_success_response, create_error_response, ErrorCodes
from app.services.franchisor_service import FranchisorService
from app.services.s3_service import S3Service
from app.core.logging import logger
from app.schemas.message_response import MessageResponse
from app.schemas.user import UserBase

router = APIRouter()


@router.get(
    "/",
    response_model=FranchisorListSuccessResponse,
    summary="List Franchisors",
    description="Retrieve a paginated list of franchisors with optional filtering, sorting, and name search",
    responses={
        200: {
            "description": "Franchisors retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Franchisors Retrieved",
                            "description": "Franchisors retrieved successfully"
                        },
                        "data": {
                            "items": [
                                {
                                    "id": "frc_123456789",
                                    "name": "Sample Franchisor",
                                    "category": "food_beverage",
                                    "region": "north_america",
                                    "budget": 150000.0,
                                    "sub_category": "restaurant",
                                    "brochure_url": "https://example.com/brochure.pdf",
                                    "is_active": True,
                                    "created_at": "2024-01-01T00:00:00Z",
                                    "updated_at": "2024-01-01T00:00:00Z"
                                }
                            ],
                            "total_count": 1,
                            "pagination": {
                                "current_page": 1,
                                "total_pages": 1,
                                "items_per_page": 20,
                                "total_items": 1
                            }
                        }
                    }
                }
            }
        },
        401: {"description": "Unauthorized"},
        500: {"description": "Internal server error"}
    },
    dependencies=[Depends(get_current_user)]
)
async def list_franchisors(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of records to return"),
    category: Optional[str] = Query(None, description="Filter by category"),
    region: Optional[str] = Query(None, description="Filter by region"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    search: Optional[str] = Query(None, description="Search franchisors by name (case-insensitive)"),
    current_user: UserBase = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """List franchisors with pagination, filtering, and name search"""
    try:
        franchisor_service = FranchisorService(db)
        franchisors, total_count = await franchisor_service.get_franchisors(
            skip=skip,
            limit=limit,
            category=category,
            region=region,
            is_active=is_active,
            search=search
        )
        
        # Calculate pagination info
        page = (skip // limit) + 1
        pages = (total_count + limit - 1) // limit
        
        return FranchisorListSuccessResponse(
            success=True,
            status="success",
            message=ResponseMessage(
                title="Franchisors Retrieved",
                description="Franchisors retrieved successfully"
            ),
            data=FranchisorListResponse(
                items=franchisors,
                total_count=total_count,
                pagination=PaginationInfo(
                    current_page=page,
                    total_pages=pages,
                    items_per_page=limit,
                    total_items=total_count
                )
            )
        )
        
    except Exception as e:
        logger.error(f"Error in list_franchisors: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve franchisors"
        )


@router.get(
    "/{franchisor_id}",
    response_model=FranchisorSuccessResponse,
    summary="Get Franchisor by ID",
    description="Retrieve a specific franchisor by its unique identifier",
    responses={
        200: {
            "description": "Franchisor retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Franchisor Retrieved",
                            "description": "Franchisor retrieved successfully"
                        },
                        "data": {
                            "id": "frc_123456789",
                            "name": "Coffee Club Melbourne",
                            "category": "food_beverage",
                            "region": "australia",
                            "budget": 250000.0,
                            "sub_category": "cafe",
                            "brochure_url": "https://s3.amazonaws.com/bucket/brochures/coffee_club_brochure.pdf",
                            "is_active": True,
                            "created_at": "2024-01-01T00:00:00Z",
                            "updated_at": "2024-01-01T12:00:00Z"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def get_franchisor(
    franchisor_id: Annotated[str, Path(description="Unique franchisor identifier", example="frc_123456789")],
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    current_user: UserBase = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Retrieve a specific franchisor by its unique identifier.
    
    This endpoint returns detailed information about a franchisor including
    all its properties and metadata.
    """
    try:
        franchise_service = FranchisorService(db)
        franchisor = await franchise_service.get_franchisor_by_id(franchisor_id)
        
        if not franchisor:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Franchisor Not Found",
                message_description=f"Franchisor with ID {franchisor_id} not found",
                status_code=404
            )

        return create_success_response(
            data=franchisor.model_dump(),
            message_title="Franchisor Retrieved",
            message_description="Franchisor retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error retrieving franchisor {franchisor_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Retrieving Franchisor",
            message_description="An error occurred while retrieving the franchisor",
            status_code=500
        )


@router.post(
    "/",
    response_model=FranchisorSuccessResponse,
    summary="Create New Franchisor",
    description="Create a new franchisor with comprehensive validation and authentication",
    responses={
        201: {
            "description": "Franchisor created successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Franchisor Created",
                            "description": "Franchisor created successfully"
                        },
                        "data": {
                            "id": "frc_987654321",
                            "name": "Jim's Mowing Sydney",
                            "category": "home_services",
                            "region": "australia",
                            "budget": 35000.0,
                            "sub_category": "lawn_care",
                            "brochure_url": None,
                            "is_active": True,
                            "created_at": "2024-01-01T00:00:00Z",
                            "updated_at": "2024-01-01T00:00:00Z"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    status_code=201,
    dependencies=[Depends(get_current_user)]
)
async def create_franchisor(
    franchisor_data: FranchisorCreateRequest,
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new franchisor with comprehensive validation.
    
    This endpoint requires authentication and validates all input data
    before creating the franchisor record.
    """
    try:
        franchise_service = FranchisorService(db)
        franchisor = await franchise_service.create_franchisor(franchisor_data)

        # Get the full franchisor response with relationship data
        franchisor_response = await franchise_service.get_franchisor_by_id(str(franchisor.id))

        return create_success_response(
            data=franchisor_response.model_dump() if franchisor_response else None,
            message_title="Franchisor Created",
            message_description="Franchisor created successfully"
        )

    except Exception as e:
        import traceback
        logger.error(f"Error creating franchisor: {e}\n{traceback.format_exc()}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Creating Franchisor",
            message_description="An error occurred while creating the franchisor",
            status_code=500
        )


@router.put(
    "/{franchisor_id}",
    summary="Update Franchisor",
    description="Update an existing franchisor with optional brochure upload",
    responses={
        200: {
            "description": "Franchisor updated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Franchisor Updated",
                            "description": "Franchisor updated successfully"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def update_franchisor(
    franchisor_id: Annotated[str, Path(description="Unique franchisor identifier", example="frc_123456789")],
    name: Optional[str] = Form(None, description="Franchisor name"),

    # UUID-based category relationships
    category_id: Optional[str] = Form(None, description="Category UUID from category table"),
    subcategory_id: Optional[str] = Form(None, description="Subcategory UUID from subcategory table"),

    region: Optional[str] = Form(None, description="Franchisor region"),
    budget: Optional[float] = Form(None, description="Franchisor budget"),
    is_active: Optional[bool] = Form(None, description="Franchisor active status"),
    brochure_file: Optional[UploadFile] = File(None, description="Optional brochure file (PDF, DOC, DOCX, JPG, PNG)"),
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    db: AsyncSession = Depends(get_db)
):
    """
    Update an existing franchisor with comprehensive validation and optional brochure upload.
    
    This endpoint requires authentication and validates all input data
    before updating the franchisor record. If a brochure file is provided,
    it will be uploaded to S3 and the franchisor's brochure_url will be updated.
    """
    try:
        # Create FranchisorUpdateRequest from form data
        franchisor_data = FranchisorUpdateRequest(
            name=name,
            category_id=category_id,
            subcategory_id=subcategory_id,
            region=FranchisorRegion(region) if region else None,
            budget=budget,
            is_active=is_active
        )
        
        franchise_service = FranchisorService(db)
        franchisor = await franchise_service.update_franchisor(franchisor_id, franchisor_data, brochure_file)
        
        if not franchisor:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Franchisor Not Found",
                message_description=f"Franchisor with ID {franchisor_id} not found",
                status_code=404
            )

        message_description = "Franchisor updated successfully"
        if brochure_file:
            message_description += " with new brochure"

        return create_success_response(
            data=None,
            message_title="Franchisor Updated",
            message_description=message_description
        )

    except Exception as e:
        logger.error(f"Error updating franchisor {franchisor_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Updating Franchisor",
            message_description="An error occurred while updating the franchisor",
            status_code=500
        )


@router.delete(
    "/{franchisor_id}",
    response_model=FranchisorDeleteSuccessResponse,
    summary="Delete Franchisor",
    description="Delete a franchisor with authentication and authorization checks",
    responses={
        200: {
            "description": "Franchisor deleted successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Franchisor Deleted",
                            "description": "Franchisor deleted successfully"
                        },
                        "data": {
                            "franchisor_id": "frc_123456789",
                            "deleted_at": "2024-01-01T12:00:00Z",
                            "message": "Franchisor has been permanently deleted"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def delete_franchisor(
    franchisor_id: Annotated[str, Path(description="Unique franchisor identifier", example="frc_123456789")],
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a franchisor with authentication and authorization checks.
    
    This endpoint requires authentication and will permanently delete
    the franchisor record from the database.
    """
    try:
        franchise_service = FranchisorService(db)
        success = await franchise_service.delete_franchisor(franchisor_id)
        
        if not success:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Franchisor Not Found",
                message_description=f"Franchisor with ID {franchisor_id} not found",
                status_code=404
            )

        from datetime import datetime
        response_data = {
            "franchisor_id": franchisor_id,
            "deleted_at": datetime.utcnow(),
            "message": "Franchisor has been permanently deleted"
        }

        return create_success_response(
            data=response_data,
            message_title="Franchisor Deleted",
            message_description="Franchisor deleted successfully"
        )

    except Exception as e:
        logger.error(f"Error deleting franchisor {franchisor_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Deleting Franchisor",
            message_description="An error occurred while deleting the franchisor",
            status_code=500
        )


@router.post(
    "/import",
    response_model=FranchisorImportSuccessResponse,
    summary="Import Franchisors from CSV",
    description="Bulk import franchisors from CSV file with validation",
    responses={
        200: {
            "description": "CSV import completed",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "CSV Import Completed",
                            "description": "CSV import completed successfully"
                        },
                        "data": {
                            "total_rows": 10,
                            "successful_imports": 8,
                            "failed_imports": 2,
                            "errors": [
                                {
                                    "row": 3,
                                    "field": "name",
                                    "message": "Name is required"
                                }
                            ]
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def import_franchisors(
    file: UploadFile = File(..., description="CSV file containing franchisor data"),
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    db: AsyncSession = Depends(get_db)
):
    """
    Import franchisors from a CSV file with comprehensive validation.
    
    The CSV file should contain the following columns:
    - name (required): Franchisor name
    - category (required): Franchisor category
    - region (optional): Franchisor region
    - budget (optional): Budget amount
    - sub_category (optional): Sub-category
    - is_active (optional): Active status (true/false)
    """
    try:
        # Validate file type
        if not file.filename.endswith('.csv'):
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="Invalid File Type",
                message_description="Only CSV files are allowed for import",
                status_code=400
            )
        
        # Read CSV content
        content = await file.read()
        csv_text = content.decode('utf-8')
        
        # Parse CSV
        csv_reader = csv.DictReader(io.StringIO(csv_text))
        rows = list(csv_reader)
        
        if not rows:
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="Empty CSV File",
                message_description="The CSV file contains no data",
                status_code=400
            )
        
        franchise_service = FranchisorService(db)
        successful_imports = 0
        failed_imports = 0
        errors = []
        
        # Process each row
        for row_num, row in enumerate(rows, start=2):  # Start from 2 because row 1 is header
            try:
                # Validate required fields
                if not row.get('name'):
                    errors.append({
                        "row": row_num,
                        "field": "name",
                        "message": "Name is required"
                    })
                    failed_imports += 1
                    continue
                
                # Category ID is optional for CSV import
                category_id = row.get('category_id')
                subcategory_id = row.get('subcategory_id')
                
                # Validate region if provided
                region = None
                if row.get('region'):
                    try:
                        region = FranchisorRegion(row['region'])
                    except ValueError:
                        errors.append({
                            "row": row_num,
                            "field": "region",
                            "message": f"Invalid region: {row['region']}"
                        })
                        failed_imports += 1
                        continue
                
                # Validate budget if provided
                budget = None
                if row.get('budget'):
                    try:
                        budget = float(row['budget'])
                        if budget < 0:
                            raise ValueError("Budget cannot be negative")
                    except ValueError:
                        errors.append({
                            "row": row_num,
                            "field": "budget",
                            "message": f"Invalid budget: {row['budget']}"
                        })
                        failed_imports += 1
                        continue
                

                
                # Validate is_active if provided
                is_active = True
                if row.get('is_active'):
                    is_active_str = row['is_active'].lower()
                    if is_active_str in ['true', '1', 'yes']:
                        is_active = True
                    elif is_active_str in ['false', '0', 'no']:
                        is_active = False
                    else:
                        errors.append({
                            "row": row_num,
                            "field": "is_active",
                            "message": f"Invalid is_active value: {row['is_active']}"
                        })
                        failed_imports += 1
                        continue
                
                # Create franchisor data
                franchisor_data = {
                    "name": row['name'],
                    "category_id": category_id,
                    "subcategory_id": subcategory_id,
                    "region": region.value if region else None,
                    "budget": budget,
                    "is_active": is_active
                }
                
                # Create franchisor
                await franchise_service.create_franchisor(
                    FranchisorCreateRequest(**franchisor_data)
                )
                successful_imports += 1
                
            except Exception as e:
                errors.append({
                    "row": row_num,
                    "field": "general",
                    "message": f"Error processing row: {str(e)}"
                })
                failed_imports += 1
        
        response_data = {
            "total_rows": len(rows),
            "successful_imports": successful_imports,
            "failed_imports": failed_imports,
            "errors": errors
        }
        
        return create_success_response(
            data=response_data,
            message_title="CSV Import Completed",
            message_description=f"Successfully imported {successful_imports} franchisors"
        )
        
    except Exception as e:
        logger.error(f"Error importing franchisors from CSV: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Importing Franchisors",
            message_description="An error occurred while importing franchisors from CSV",
            status_code=500
        )


@router.post(
    "/{franchisor_id}/upload-brochure",
    response_model=FranchisorSuccessResponse,
    summary="Upload Franchisor Brochure",
    description="Upload a brochure file for a franchisor",
    responses={
        200: {
            "description": "Brochure uploaded successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Brochure Uploaded",
                            "description": "Brochure uploaded successfully"
                        },
                        "data": {
                            "id": "frc_123456789",
                            "name": "Coffee Club Melbourne",
                            "category": "food_beverage",
                            "region": "australia",
                            "budget": 250000.0,
                            "sub_category": "cafe",
                            "brochure_url": "https://s3.amazonaws.com/bucket/brochures/20240101_123456_abc123.pdf",
                            "is_active": True,
                            "created_at": "2024-01-01T00:00:00Z",
                            "updated_at": "2024-01-01T12:00:00Z"
                        }
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def upload_brochure(
    franchisor_id: Annotated[str, Path(description="Unique franchisor identifier", example="frc_123456789")],
    file: UploadFile = File(..., description="Brochure file (PDF, DOC, DOCX, JPG, PNG)"),
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    db: AsyncSession = Depends(get_db)
):
    """
    Upload a brochure file for a franchisor.
    
    This endpoint uploads the file to S3 and updates the franchisor's brochure_url.
    Supported file types: PDF, DOC, DOCX, JPG, PNG
    Maximum file size: 10MB
    """
    try:
        # Check if franchisor exists
        franchise_service = FranchisorService(db)
        franchisor = await franchise_service.get_franchisor_by_id(franchisor_id)
        
        if not franchisor:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Franchisor Not Found",
                message_description=f"Franchisor with ID {franchisor_id} not found",
                status_code=404
            )
        
        # Upload file to S3
        s3_service = S3Service()
        brochure_filename = await s3_service.upload_file(file, prefix="brochures")
        
        # Update franchisor with brochure filename
        updated_franchisor = await franchise_service.update_brochure_url(franchisor_id, brochure_filename)
        
        if not updated_franchisor:
            return create_error_response(
                error_code=ErrorCodes.DATABASE_ERROR,
                message_title="Error Updating Franchisor",
                message_description="Failed to update franchisor with brochure filename",
                status_code=500
            )
        
        # Get the updated franchisor with proper response format
        updated_franchisor_response = await franchise_service.get_franchisor_by_id(franchisor_id)
        
        return create_success_response(
            data=updated_franchisor_response.model_dump() if updated_franchisor_response else None,
            message_title="Brochure Uploaded",
            message_description="Brochure uploaded successfully"
        )
        
    except Exception as e:
        logger.error(f"Error uploading brochure for franchisor {franchisor_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Uploading Brochure",
            message_description="An error occurred while uploading the brochure",
            status_code=500
        )


@router.get(
    "/categories",
    summary="Get Available Categories",
    description="Get list of available categories for franchisor creation/update",
    responses={
        200: {
            "description": "Categories retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Categories Retrieved",
                            "description": "Available categories retrieved successfully"
                        },
                        "data": [
                            {
                                "id": 1,
                                "name": "Food & Beverage",
                                "description": "All food-related businesses",
                                "is_active": True,
                                "is_deleted": False,
                                "created_at": "2024-01-01T00:00:00Z",
                                "updated_at": "2024-01-01T00:00:00Z"
                            }
                        ]
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def get_franchisor_categories(
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    db: AsyncSession = Depends(get_db)
):
    """Get list of available categories for franchisor creation/update"""
    try:
        from app.services.category_service import CategoryService
        from starlette.responses import JSONResponse

        category_service = CategoryService(db)
        categories = await category_service.list_categories(skip=0, limit=1000)

        # Check if the service returned an error response
        if isinstance(categories, JSONResponse):
            logger.error("Category service returned an error response")
            return create_error_response(
                error_code=ErrorCodes.DATABASE_ERROR,
                message_title="Error Retrieving Categories",
                message_description="An error occurred while retrieving categories",
                status_code=500
            )

        return create_success_response(
            data=[category.model_dump() for category in categories],
            message_title="Categories Retrieved",
            message_description="Available categories retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error retrieving categories: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Retrieving Categories",
            message_description="An error occurred while retrieving categories",
            status_code=500
        )


@router.get(
    "/categories/{category_id}/subcategories",
    summary="Get Available Subcategories",
    description="Get list of available subcategories for a specific category",
    responses={
        200: {
            "description": "Subcategories retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "status": "success",
                        "message": {
                            "title": "Subcategories Retrieved",
                            "description": "Available subcategories retrieved successfully"
                        },
                        "data": [
                            {
                                "id": "123e4567-e89b-12d3-a456-426614174001",
                                "name": "Cafe",
                                "description": "Coffee shops and cafes",
                                "is_active": True,
                                "is_deleted": False,
                                "category_id": "123e4567-e89b-12d3-a456-************",
                                "created_at": "2024-01-01T00:00:00Z",
                                "updated_at": "2024-01-01T00:00:00Z"
                            }
                        ]
                    }
                }
            }
        },
        **COMMON_RESPONSES
    },
    dependencies=[Depends(get_current_user)]
)
async def get_franchisor_subcategories(
    category_id: Annotated[str, Path(description="Category UUID", example="123e4567-e89b-12d3-a456-************")],
    current_user: UserBase = Depends(get_current_user),
    x_request_id: Annotated[Optional[str], Header(description="Optional request ID for tracking")] = None,
    db: AsyncSession = Depends(get_db)
):
    """Get list of available subcategories for a specific category"""
    try:
        from app.services.subcategory_service import SubcategoryService
        from starlette.responses import JSONResponse

        subcategory_service = SubcategoryService(db)
        subcategories = await subcategory_service.list_subcategories_by_category(category_id, skip=0, limit=1000)

        # Check if the service returned an error response
        if isinstance(subcategories, JSONResponse):
            logger.error(f"Subcategory service returned an error response for category {category_id}")
            return create_error_response(
                error_code=ErrorCodes.DATABASE_ERROR,
                message_title="Error Retrieving Subcategories",
                message_description="An error occurred while retrieving subcategories",
                status_code=500
            )

        return create_success_response(
            data=[subcategory.model_dump() for subcategory in subcategories],
            message_title="Subcategories Retrieved",
            message_description="Available subcategories retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error retrieving subcategories for category {category_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Retrieving Subcategories",
            message_description="An error occurred while retrieving subcategories",
            status_code=500
        )