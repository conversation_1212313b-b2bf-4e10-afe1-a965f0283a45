from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse
from typing import List, Optional
from app.schemas.category import CategoryCreateRequest, CategoryUpdateRequest, CategoryResponse
from app.core.factory import get_category_service
from app.services.category_service import CategoryService
from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.api_standards import APIStandards
from app.schemas.base_response import StandardResponse
from app.core.logging import logger
from app.core.responses.models import ErrorCodes

router = APIRouter()

@router.post("/categories", response_model=StandardResponse[CategoryResponse], status_code=status.HTTP_201_CREATED)
async def create_category(
    category_in: CategoryCreateRequest,
    service: CategoryService = Depends(get_category_service),
    current_user: dict = Depends(get_current_user)
):
    """Create a new category."""
    try:
        category = await service.create_category(category_in)
        # If error response (dict or JSONResponse), return it directly
        if isinstance(category, (dict, JSONResponse)):
            return category

        # Log successful creation
        logger.info(f"Category created successfully: {category.name}", extra={
            "context": {"category_id": str(category.id), "user_id": str(current_user.id) if hasattr(current_user, 'id') else 'unknown'}
        })

        # Convert ORM object to response format
        category_dict = category.__dict__.copy()
        if 'id' in category_dict and category_dict['id'] is not None:
            category_dict['id'] = str(category_dict['id'])

        # Validate required fields are present
        required_fields = ["id", "name", "description", "is_active", "is_deleted", "created_at", "updated_at"]
        missing = [f for f in required_fields if f not in category_dict]
        if missing:
            logger.error(f"Missing fields in category ORM object: {missing}", extra={
                "context": {"category_dict": category_dict, "missing_fields": missing}
            })
            return APIStandards.create_error_response(
                error_message="Internal error: Missing required fields in category data",
                error_title="Data Integrity Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=ErrorCodes.UNKNOWN_ERROR
            )

        return APIStandards.create_success_response(
            data=CategoryResponse(**category_dict),
            message="Category created successfully",
            title="Category Created",
            status_code=status.HTTP_201_CREATED
        )
    except Exception as e:
        logger.error(f"Unexpected error creating category: {str(e)}", exc_info=True, extra={
            "context": {"category_name": category_in.name}
        })
        return APIStandards.create_error_response(
            error_message="An error occurred while creating the category",
            error_title="Creation Failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )

@router.get("/categories", response_model=StandardResponse[List[CategoryResponse]])
async def list_categories(
    search: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    service: CategoryService = Depends(get_category_service),
    current_user: dict = Depends(get_current_user)
):
    """List all categories (with optional search)."""
    categories = await service.list_categories(skip=skip, limit=limit, search=search)
    if isinstance(categories, dict):
        return categories
    category_list = []
    for c in categories:
        c_dict = c.__dict__.copy()
        if 'id' in c_dict and c_dict['id'] is not None:
            c_dict['id'] = str(c_dict['id'])
        category_list.append(CategoryResponse(**c_dict))
    return APIStandards.create_success_response(
        data=category_list,
        message="Categories retrieved successfully",
        title="Categories List"
    )

@router.get("/categories/{id}", response_model=StandardResponse[CategoryResponse])
async def get_category(
    id: str,
    service: CategoryService = Depends(get_category_service),
    current_user: dict = Depends(get_current_user)
):
    """Get a single category by UUID."""
    category = await service.get_category(id)
    if isinstance(category, dict):
        return category
    category_dict = category.__dict__.copy()
    if 'id' in category_dict and category_dict['id'] is not None:
        category_dict['id'] = str(category_dict['id'])
    return APIStandards.create_success_response(
        data=CategoryResponse(**category_dict),
        message="Category retrieved successfully",
        title="Category Details"
    )

@router.put("/categories/{id}", response_model=StandardResponse[CategoryResponse])
async def update_category(
    id: str,
    category_in: CategoryUpdateRequest,
    service: CategoryService = Depends(get_category_service),
    current_user: dict = Depends(get_current_user)
):
    """Update a category by UUID."""
    category = await service.update_category(id, category_in)
    if isinstance(category, dict):
        return category
    category_dict = category.__dict__.copy()
    if 'id' in category_dict and category_dict['id'] is not None:
        category_dict['id'] = str(category_dict['id'])
    return APIStandards.create_success_response(
        data=CategoryResponse(**category_dict),
        message="Category updated successfully",
        title="Category Updated"
    )