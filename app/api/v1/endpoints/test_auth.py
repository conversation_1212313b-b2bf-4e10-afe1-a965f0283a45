"""
Test authentication endpoint
"""

from fastapi import APIRouter, Depends
from app.core.security.auth import get_current_user
from app.schemas.user import UserResponse

router = APIRouter()

@router.get("/test-auth")
async def test_auth(current_user: UserResponse = Depends(get_current_user)):
    """Test authentication endpoint"""
    return {
        "success": True,
        "message": "Authentication working",
        "user": current_user
    } 