"""
Documents API endpoints for RAG system
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from sqlalchemy.orm import Session

from app.core.database.connection import get_db
from app.core.security.auth import get_current_user
from app.schemas.document import DocumentCreateRequest, DocumentResponse, DocumentSearchRequest, DocumentUpdateRequest
from app.services.document_service import DocumentService
from app.services.user_service import UserService
from app.services.rag_service import RAGService

router = APIRouter()

@router.get("/", response_model=List[DocumentResponse])
async def get_documents(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    franchisor_id: Optional[str] = None,
    is_processed: Optional[bool] = None,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get documents with filtering"""
    user_service = UserService(db)
    document_service = DocumentService(db)
    
    # Get current user profile
    current_user_profile = await user_service.get_user_by_auth_id(current_user["sub"])
    if not current_user_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )
    
    documents = await document_service.get_documents(
        skip=skip,
        limit=limit,
        franchisor_id=franchisor_id,
        is_processed=is_processed,
        user_role=current_user_profile.role,
        user_id=current_user_profile.id
    )
    return documents

@router.get("/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: str,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get document by ID"""
    user_service = UserService(db)
    document_service = DocumentService(db)
    
    # Get current user profile
    current_user_profile = await user_service.get_user_by_auth_id(current_user["sub"])
    if not current_user_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )
    
    document = await document_service.get_document_by_id(
        document_id,
        user_role=current_user_profile.role,
        user_id=current_user_profile.id
    )
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    return document

@router.post("/", response_model=DocumentResponse)
async def create_document(document: DocumentCreateRequest, db: Session = Depends(get_db)):
    service = DocumentService(db)
    return service.create_document(document)

@router.post("/upload", response_model=DocumentResponse)
async def upload_document(
    file: UploadFile = File(...),
    franchisor_id: Optional[str] = None,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Upload document file"""
    user_service = UserService(db)
    document_service = DocumentService(db)
    
    # Get current user profile
    current_user_profile = await user_service.get_user_by_auth_id(current_user["sub"])
    if not current_user_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )
    
    # Validate file type
    allowed_types = ["text/plain", "application/pdf", "application/msword", 
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document"]
    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Unsupported file type"
        )
    
    try:
        document = await document_service.upload_document(
            file=file,
            franchisor_id=franchisor_id
        )
        return document
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload document: {str(e)}"
        )

@router.post("/{document_id}/process")
async def process_document(
    document_id: str,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Process document for RAG system"""
    user_service = UserService(db)
    rag_service = RAGService(db)
    
    # Get current user profile
    current_user_profile = await user_service.get_user_by_auth_id(current_user["sub"])
    if not current_user_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )
    
    try:
        # Process document and generate embeddings
        result = await rag_service.process_document(document_id)
        return {
            "message": "Document processed successfully",
            "chunks_created": result["chunks_created"],
            "embeddings_generated": result["embeddings_generated"]
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process document: {str(e)}"
        )

@router.post("/search")
async def search_documents(
    search_request: DocumentSearchRequest,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Search documents using vector similarity"""
    user_service = UserService(db)
    rag_service = RAGService(db)
    
    # Get current user profile
    current_user_profile = await user_service.get_user_by_auth_id(current_user["sub"])
    if not current_user_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )
    
    try:
        results = await rag_service.search_documents(
            query=search_request.query,
            limit=search_request.limit,
            similarity_threshold=search_request.similarity_threshold,
            franchisor_id=search_request.franchisor_id,
            user_role=current_user_profile.role,
            user_id=current_user_profile.id
        )
        return {
            "query": search_request.query,
            "results": results,
            "total_results": len(results)
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search documents: {str(e)}"
        )

@router.post("/generate-response")
async def generate_ai_response(
    query: str,
    franchisor_id: Optional[str] = None,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate AI response using RAG system"""
    user_service = UserService(db)
    rag_service = RAGService(db)
    
    # Get current user profile
    current_user_profile = await user_service.get_user_by_auth_id(current_user["sub"])
    if not current_user_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )
    
    try:
        response = await rag_service.generate_response(
            query=query,
            franchisor_id=franchisor_id,
            user_role=current_user_profile.role,
            user_id=current_user_profile.id
        )
        return {
            "query": query,
            "response": response["response"],
            "sources": response["sources"],
            "confidence": response["confidence"]
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate response: {str(e)}"
        )

@router.put("/{document_id}", response_model=DocumentResponse)
def update_document(document_id: str, document: DocumentUpdateRequest, db: Session = Depends(get_db)):
    service = DocumentService(db)
    return service.update_document(document_id, document)

@router.delete("/{document_id}", response_model=DocumentResponse)
def delete_document(document_id: str, db: Session = Depends(get_db)):
    service = DocumentService(db)
    return service.delete_document(document_id)
