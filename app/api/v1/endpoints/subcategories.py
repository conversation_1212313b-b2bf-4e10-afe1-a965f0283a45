from fastapi import APIRouter, Depends, status
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import List, Optional
from app.schemas.subcategory import SubcategoryCreateRequest, SubcategoryUpdateRequest, SubcategoryResponse
from app.core.factory import get_subcategory_service
from app.services.subcategory_service import SubcategoryService
from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.api_standards import APIStandards
from app.schemas.base_response import StandardResponse
from app.core.logging import logger
from app.core.responses.models import ErrorCodes

router = APIRouter()

@router.post("/categories/{category_id}/subcategories", response_model=StandardResponse[SubcategoryResponse], status_code=status.HTTP_201_CREATED)
async def create_subcategory(
    category_id: str,
    subcategory_in: SubcategoryCreateRequest,
    service: SubcategoryService = Depends(get_subcategory_service),
    current_user: dict = Depends(get_current_user)
):
    """Create a subcategory under a category using UUID."""
    try:
        subcategory = await service.create_subcategory(category_id, subcategory_in)
        # If error response (dict or JSONResponse), return it directly
        if isinstance(subcategory, (dict, JSONResponse)):
            return subcategory

        # Log successful creation
        logger.info(f"Subcategory created successfully: {subcategory.name}", extra={
            "context": {
                "subcategory_id": str(subcategory.id),
                "category_id": category_id,
                "user_id": str(current_user.id) if hasattr(current_user, 'id') else 'unknown'
            }
        })

        # Convert ORM object to response format
        subcategory_dict = subcategory.__dict__.copy()
        if 'id' in subcategory_dict and subcategory_dict['id'] is not None:
            subcategory_dict['id'] = str(subcategory_dict['id'])
        if 'category_id' in subcategory_dict and subcategory_dict['category_id'] is not None:
            subcategory_dict['category_id'] = str(subcategory_dict['category_id'])

        # Validate required fields are present
        required_fields = ["id", "name", "description", "is_active", "is_deleted", "category_id", "created_at", "updated_at"]
        missing = [f for f in required_fields if f not in subcategory_dict]
        if missing:
            logger.error(f"Missing fields in subcategory ORM object: {missing}", extra={
                "context": {"subcategory_dict": subcategory_dict, "missing_fields": missing}
            })
            return APIStandards.create_error_response(
                error_message="Internal error: Missing required fields in subcategory data",
                error_title="Data Integrity Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=ErrorCodes.UNKNOWN_ERROR
            )

        return APIStandards.create_success_response(
            data=SubcategoryResponse(**subcategory_dict),
            message="Subcategory created successfully",
            title="Subcategory Created",
            status_code=status.HTTP_201_CREATED
        )
    except Exception as e:
        logger.error(f"Unexpected error creating subcategory: {str(e)}", exc_info=True, extra={
            "context": {"subcategory_name": subcategory_in.name, "category_id": category_id}
        })
        return APIStandards.create_error_response(
            error_message="An error occurred while creating the subcategory",
            error_title="Creation Failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )

@router.get("/categories/{category_id}/subcategories", response_model=StandardResponse[List[SubcategoryResponse]])
async def list_subcategories_by_category(
    category_id: str,
    search: Optional[str] = None,
    is_active: Optional[bool] = None,
    skip: int = 0,
    limit: int = 100,
    service: SubcategoryService = Depends(get_subcategory_service),
    current_user: dict = Depends(get_current_user)
):
    """List all subcategories of a category using UUID (with optional search and isActive filter)."""
    subcategories = await service.list_subcategories(category_id=category_id, skip=skip, limit=limit, search=search, is_active=is_active)
    if isinstance(subcategories, dict):
        return subcategories
    subcategory_list = []
    for s in subcategories:
        s_dict = s.__dict__.copy()
        if 'id' in s_dict and s_dict['id'] is not None:
            s_dict['id'] = str(s_dict['id'])
        if 'category_id' in s_dict and s_dict['category_id'] is not None:
            s_dict['category_id'] = str(s_dict['category_id'])
        subcategory_list.append(SubcategoryResponse(**s_dict))
    return APIStandards.create_success_response(
        data=subcategory_list,
        message="Subcategories retrieved successfully",
        title="Subcategories List"
    )

@router.get("/subcategories/{id}", response_model=StandardResponse[SubcategoryResponse])
async def get_subcategory(
    id: str,
    service: SubcategoryService = Depends(get_subcategory_service),
    current_user: dict = Depends(get_current_user)
):
    """Get a subcategory by UUID."""
    subcategory = await service.get_subcategory(id)
    if isinstance(subcategory, dict):
        return subcategory
    subcategory_dict = subcategory.__dict__.copy()
    if 'id' in subcategory_dict and subcategory_dict['id'] is not None:
        subcategory_dict['id'] = str(subcategory_dict['id'])
    if 'category_id' in subcategory_dict and subcategory_dict['category_id'] is not None:
        subcategory_dict['category_id'] = str(subcategory_dict['category_id'])
    return APIStandards.create_success_response(
        data=SubcategoryResponse(**subcategory_dict),
        message="Subcategory retrieved successfully",
        title="Subcategory Details"
    )

@router.get("/subcategories", response_model=StandardResponse[List[SubcategoryResponse]])
async def search_subcategories(
    search: Optional[str] = None,
    is_active: Optional[bool] = None,
    skip: int = 0,
    limit: int = 100,
    service: SubcategoryService = Depends(get_subcategory_service),
    current_user: dict = Depends(get_current_user)
):
    """Search subcategories across all categories."""
    subcategories = await service.list_subcategories(skip=skip, limit=limit, search=search, is_active=is_active)
    if isinstance(subcategories, dict):
        return subcategories
    subcategory_list = []
    for s in subcategories:
        s_dict = s.__dict__.copy()
        if 'id' in s_dict and s_dict['id'] is not None:
            s_dict['id'] = str(s_dict['id'])
        if 'category_id' in s_dict and s_dict['category_id'] is not None:
            s_dict['category_id'] = str(s_dict['category_id'])
        subcategory_list.append(SubcategoryResponse(**s_dict))
    return APIStandards.create_success_response(
        data=subcategory_list,
        message="Subcategories retrieved successfully",
        title="Subcategories List"
    )

@router.put("/subcategories/{id}", response_model=StandardResponse[SubcategoryResponse])
async def update_subcategory(
    id: str,
    subcategory_in: SubcategoryUpdateRequest,
    service: SubcategoryService = Depends(get_subcategory_service),
    current_user: dict = Depends(get_current_user)
):
    """Update a subcategory by UUID."""
    subcategory = await service.update_subcategory(id, subcategory_in)
    if isinstance(subcategory, dict):
        return subcategory
    subcategory_dict = subcategory.__dict__.copy()
    if 'id' in subcategory_dict and subcategory_dict['id'] is not None:
        subcategory_dict['id'] = str(subcategory_dict['id'])
    if 'category_id' in subcategory_dict and subcategory_dict['category_id'] is not None:
        subcategory_dict['category_id'] = str(subcategory_dict['category_id'])
    return APIStandards.create_success_response(
        data=SubcategoryResponse(**subcategory_dict),
        message="Subcategory updated successfully",
        title="Subcategory Updated"
    )