"""
API Router Configuration
Main router that includes all API endpoints with proper documentation
"""

from fastapi import APIRouter

# Import all endpoint routers
from app.api.v1.endpoints import (
    auth,
    franchisors,
    documents,
    leads,
    test_auth,
    categories,
    subcategories,
    holidays,
    messaging_rules,
    subcategories,
    logs
)

# Create main API router
api_router = APIRouter()

# Include all endpoint routers with proper tags
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["Authentication"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        422: {"description": "Validation Error"}
    }
)

api_router.include_router(
    franchisors.router,
    prefix="/franchisors",
    tags=["Franchisors"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)

api_router.include_router(
    documents.router,
    prefix="/documents",
    tags=["Documents"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)

api_router.include_router(
    leads.router,
    prefix="/leads",
    tags=["Leads"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)

api_router.include_router(
    test_auth.router,
    prefix="/test",
    tags=["Test"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        422: {"description": "Validation Error"}
    }
)

api_router.include_router(
    categories.router,
    prefix="",
    tags=["Categories"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)

api_router.include_router(
    subcategories.router,
    prefix="",
    tags=["Subcategories"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)

# Settings endpoints
api_router.include_router(
    holidays.router,
    prefix="/settings",
    tags=["Settings - Holidays"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)

api_router.include_router(
    messaging_rules.router,
    prefix="/settings",
    tags=["Settings - Messaging Rules"],
    responses={
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        422: {"description": "Validation Error"}
    }
)

# Include logs router (no authentication required)
api_router.include_router(
    logs.router,
    prefix="/logs",
    tags=["Logs"],
    responses={
        404: {"description": "Log file not found"},
        500: {"description": "Internal server error"}
    }
)

# Add root endpoint
@api_router.get(
    "/",
    tags=["api"],
    summary="API Root",
    description="Welcome to the GrowthHive API",
    responses={
        200: {
            "description": "Successful response",
            "content": {
                "application/json": {
                    "example": {
                        "message": "Welcome to GrowthHive API",
                        "version": "1.0.0",
                        "docs_url": "/docs",
                        "redoc_url": "/redoc"
                    }
                }
            }
        }
    }
)
async def root():
    """API root endpoint"""
    return {
        "message": "Welcome to GrowthHive API",
        "version": "1.0.0",
        "docs_url": "/docs",
        "redoc_url": "/redoc"
    }
