from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime

class SubcategoryCreateRequest(BaseModel):
    """Request model for creating a subcategory."""
    name: str = Field(..., example="Pizza Chains")
    description: Optional[str] = Field(None, example="Pizza restaurant franchises.")
    is_active: Optional[bool] = Field(default=True, example=True)
    is_deleted: Optional[bool] = Field(default=False, example=False)

class SubcategoryUpdateRequest(BaseModel):
    """Request model for updating a subcategory."""
    name: Optional[str] = Field(None, example="Burger Chains")
    description: Optional[str] = Field(None, example="Burger restaurant franchises.")
    is_active: Optional[bool] = Field(None, example=True)
    is_deleted: Optional[bool] = Field(None, example=False)

class SubcategoryResponse(BaseModel):
    """Response model for a subcategory with UUID."""
    id: str = Field(..., description="Subcategory UUID", example="123e4567-e89b-12d3-a456-************")
    name: str
    description: Optional[str]
    is_active: bool
    is_deleted: bool
    category_id: str = Field(..., description="Category UUID", example="123e4567-e89b-12d3-a456-************")
    created_at: datetime
    updated_at: datetime