"""Lead schemas for request/response validation"""
from typing import Optional, List
from pydantic import BaseModel, EmailStr, Field
from datetime import datetime
from decimal import Decimal
from enum import Enum

from app.schemas.base_response import ResponseMessage, PaginationInfo


class LeadStatus(str, Enum):
    """Lead qualification status"""
    NEW = "new"
    CONTACTED = "contacted"
    QUALIFIED = "qualified"
    UNQUALIFIED = "unqualified"
    CONVERTED = "converted"
    LOST = "lost"


class LeadCreateRequest(BaseModel):
    """Schema for creating a lead"""
    full_name: str = Field(..., description="Lead full name", example="<PERSON>")
    contact_number: str = Field(..., description="Lead contact number", example="+1234567890")
    email: Optional[EmailStr] = Field(None, description="Lead email address", example="<EMAIL>")
    location: Optional[str] = Field(None, description="Lead location", example="New York")
    lead_source: Optional[str] = Field(None, description="Lead source", example="Website")
    franchise_preference: Optional[str] = Field(None, description="Franchise preference", example="Food")
    budget_preference: Optional[Decimal] = Field(None, description="Budget preference", example=75000.00)
    zoho_lead_id: Optional[str] = Field(None, description="Zoho CRM lead ID")
    qualification_status: Optional[LeadStatus] = Field(LeadStatus.NEW, description="Lead qualification status")
    is_active: Optional[bool] = Field(True, description="Whether the lead is active")
    is_deleted: Optional[bool] = Field(False, description="Whether the lead is deleted")


class LeadUpdateRequest(BaseModel):
    """Schema for updating a lead"""
    full_name: Optional[str] = Field(None, description="Lead full name", example="John Doe")
    contact_number: Optional[str] = Field(None, description="Lead contact number", example="+1234567890")
    email: Optional[EmailStr] = Field(None, description="Lead email address", example="<EMAIL>")
    location: Optional[str] = Field(None, description="Lead location", example="New York")
    lead_source: Optional[str] = Field(None, description="Lead source", example="Website")
    franchise_preference: Optional[str] = Field(None, description="Franchise preference", example="Food")
    budget_preference: Optional[Decimal] = Field(None, description="Budget preference", example=75000.00)
    qualification_status: Optional[LeadStatus] = Field(None, description="Lead qualification status")
    is_active: Optional[bool] = Field(None, description="Whether the lead is active")
    is_deleted: Optional[bool] = Field(None, description="Whether the lead is deleted")


class LeadResponse(BaseModel):
    """Schema for lead response"""
    id: str = Field(..., description="Lead ID", example="550e8400-e29b-41d4-a716-446655440000")
    zoho_lead_id: Optional[str] = Field(None, description="Zoho CRM lead ID")
    full_name: str = Field(..., description="Lead full name", example="John Doe")
    contact_number: str = Field(..., description="Lead contact number", example="+1234567890")
    email: Optional[str] = Field(None, description="Lead email address", example="<EMAIL>")
    location: Optional[str] = Field(None, description="Lead location", example="New York")
    lead_source: Optional[str] = Field(None, description="Lead source", example="Website")
    franchise_preference: Optional[str] = Field(None, description="Franchise preference", example="Food")
    budget_preference: Optional[Decimal] = Field(None, description="Budget preference", example=75000.00)
    qualification_status: LeadStatus = Field(..., description="Lead qualification status")
    is_active: bool = Field(..., description="Whether the lead is active")
    is_deleted: bool = Field(..., description="Whether the lead is deleted")
    created_at: datetime = Field(..., description="Lead creation timestamp")
    updated_at: datetime = Field(..., description="Lead last update timestamp")

    class Config:
        from_attributes = True


class LeadListResponse(BaseModel):
    """Schema for lead list response (Franchisor style)"""
    items: List[LeadResponse] = Field(..., description="List of leads")
    total_count: int = Field(..., description="Total number of leads")
    pagination: PaginationInfo = Field(..., description="Pagination information")


class LeadSuccessResponse(BaseModel):
    """Standard success response for lead operations"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: LeadResponse = Field(..., description="Lead data")


class LeadListSuccessResponse(BaseModel):
    """Standard success response for lead list operations"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: LeadListResponse = Field(..., description="Lead list data")


class LeadDeleteSuccessResponse(BaseModel):
    """Standard success response for lead deletion"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: dict = Field(..., description="Deletion confirmation data")





# Legacy schemas for backward compatibility
class LeadBase(BaseModel):
    """Base lead schema"""
    full_name: str = Field(..., description="Lead full name")
    contact_number: str = Field(..., description="Lead contact number")
    email: Optional[EmailStr] = Field(None, description="Lead email address")
    location: Optional[str] = Field(None, description="Lead location")
    lead_source: Optional[str] = Field(None, description="Lead source")
    franchise_preference: Optional[str] = Field(None, description="Franchise preference")
    budget_preference: Optional[Decimal] = Field(None, description="Budget preference")


class LeadCreate(LeadBase):
    """Schema for creating a lead"""
    zoho_lead_id: Optional[str] = Field(None, description="Zoho CRM lead ID")
    qualification_status: Optional[LeadStatus] = Field(LeadStatus.NEW, description="Lead qualification status")


class LeadUpdate(BaseModel):
    """Schema for updating a lead"""
    full_name: Optional[str] = Field(None, description="Lead full name")
    contact_number: Optional[str] = Field(None, description="Lead contact number")
    email: Optional[EmailStr] = Field(None, description="Lead email address")
    location: Optional[str] = Field(None, description="Lead location")
    lead_source: Optional[str] = Field(None, description="Lead source")
    franchise_preference: Optional[str] = Field(None, description="Franchise preference")
    budget_preference: Optional[Decimal] = Field(None, description="Budget preference")
    qualification_status: Optional[LeadStatus] = Field(None, description="Lead qualification status")


class QuestionBase(BaseModel):
    """Base question schema"""
    question_text: str = Field(..., description="Question text")
    question_type: Optional[str] = Field(None, description="Question type")
    is_required: bool = Field(False, description="Is question required")
    order_sequence: Optional[int] = Field(None, description="Question order sequence")


class QuestionCreate(QuestionBase):
    """Schema for creating a question"""
    is_active: bool = Field(True, description="Is question active")


class QuestionResponse(QuestionBase):
    """Schema for question response"""
    id: str = Field(..., description="Question ID")
    is_active: bool = Field(..., description="Is question active")
    created_at: datetime = Field(..., description="Question creation timestamp")

    class Config:
        from_attributes = True


class LeadResponseBase(BaseModel):
    """Base lead response schema"""
    response_text: Optional[str] = Field(None, description="Response text")


class LeadResponseCreate(LeadResponseBase):
    """Schema for creating lead response"""
    lead_id: str = Field(..., description="Lead ID")
    question_id: str = Field(..., description="Question ID")


class BulkUploadResponse(BaseModel):
    """Schema for bulk upload response"""
    total_processed: int = Field(..., description="Total number of rows processed")
    successful_imports: int = Field(..., description="Number of successful imports")
    duplicates_found: int = Field(..., description="Number of duplicates found")
    errors_found: int = Field(..., description="Number of errors found")
    duplicates: List[str] = Field(..., description="List of duplicate entries")
    errors: List[str] = Field(..., description="List of errors")


class BulkUploadSuccessResponse(BaseModel):
    """Standard success response for bulk upload operations"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: BulkUploadResponse = Field(..., description="Bulk upload results")


class CommunicationResponse(BaseModel):
    """Schema for communication response"""
    id: str = Field(..., description="Communication ID")
    communication_type: str = Field(..., description="Communication type")
    subject: Optional[str] = Field(None, description="Communication subject")
    content: Optional[str] = Field(None, description="Communication content")
    direction: Optional[str] = Field(None, description="Communication direction")
    user_id: Optional[str] = Field(None, description="User ID who created the communication")
    created_at: datetime = Field(..., description="Communication creation timestamp")
    updated_at: datetime = Field(..., description="Communication last update timestamp")


class CommunicationHistoryResponse(BaseModel):
    """Schema for communication history response"""
    communications: List[CommunicationResponse] = Field(..., description="List of communications")
    total_count: int = Field(..., description="Total number of communications")


class CommunicationHistorySuccessResponse(BaseModel):
    """Standard success response for communication history operations"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: CommunicationHistoryResponse = Field(..., description="Communication history data")


class LeadResponseResponse(LeadResponseBase):
    """Schema for lead response response"""
    id: str = Field(..., description="Lead response ID")
    lead_id: str = Field(..., description="Lead ID")
    question_id: str = Field(..., description="Question ID")
    answered_at: datetime = Field(..., description="Response timestamp")

    class Config:
        from_attributes = True
