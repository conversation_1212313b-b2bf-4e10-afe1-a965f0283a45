"""
Franchisor model for database operations
"""

from sqlalchemy import Column, String, Boolean, Float, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database.connection import Base
from sqlalchemy.dialects.postgresql import UUID
import uuid


class Franchisor(Base):
    """Franchisor model for storing franchisor information"""

    __tablename__ = "franchisors"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(255), nullable=False, index=True)

    # Foreign key relationships to Category and Subcategory (UUID)
    category_id = Column(UUID(as_uuid=True), ForeignKey("category.id", ondelete="SET NULL"), nullable=True, index=True)
    subcategory_id = Column(UUID(as_uuid=True), ForeignKey("subcategory.id", ondelete="SET NULL"), nullable=True, index=True)

    region = Column(String(100), nullable=True, index=True)
    budget = Column(Float, nullable=True)
    brochure_url = Column(String(500), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    category_rel = relationship("Category", foreign_keys=[category_id], lazy="select")
    subcategory_rel = relationship("Subcategory", foreign_keys=[subcategory_id], lazy="select")

    def __repr__(self):
        return f"<Franchisor(id={self.id}, name={self.name}, category_id={self.category_id})>"