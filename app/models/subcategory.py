from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.core.database.connection import Base
from datetime import datetime
import uuid

class Subcategory(Base):
    """Subcategory model linked to a parent category with UUID primary key."""
    __tablename__ = "subcategory"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False)
    category_id = Column(UUID(as_uuid=True), ForeignKey("category.id", ondelete="CASCADE"), nullable=False, index=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationship to Category
    category = relationship("Category", lazy="select")

    def __repr__(self):
        return f"<Subcategory(id={self.id}, name={self.name}, category_id={self.category_id})>"