"""
Document Model
SQLAlchemy model for document management
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, DateTime, Text, ForeignKey, func
from sqlalchemy.dialects.postgresql import UUID
from app.core.database.connection import Base

class Document(Base):
    """Document model for GrowthHive"""
    __tablename__ = "documents"
    
    id = Column(UUID(as_uuid=True), primary_key=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    file_path = Column(String(500), nullable=False)
    file_type = Column(String(50))
    file_size = Column(String(20))
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    franchisor_id = Column(UUID(as_uuid=True), ForeignKey("franchisors.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    def __repr__(self):
        return f"<Document(id={self.id}, filename={self.name})>" 