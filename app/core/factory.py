from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.base import get_db
from app.repositories.category_repository import CategoryRepository
from app.repositories.subcategory_repository import SubcategoryRepository
from app.repositories.holiday_repository import HolidayRepository
from app.repositories.messaging_rule_repository import MessagingRuleRepository
from app.services.category_service import CategoryService
from app.services.subcategory_service import SubcategoryService
from app.services.holiday_service import HolidayService
from app.services.messaging_rule_service import MessagingRuleService


def get_category_repository(db: AsyncSession = Depends(get_db)) -> CategoryRepository:
    """Factory for CategoryRepository."""
    return CategoryRepository(db)

def get_subcategory_repository(db: AsyncSession = Depends(get_db)) -> SubcategoryRepository:
    """Factory for SubcategoryRepository."""
    return SubcategoryRepository(db)

def get_category_service(
    repo: CategoryRepository = Depends(get_category_repository),
) -> CategoryService:
    """Factory for CategoryService."""
    return CategoryService(repo)

def get_subcategory_service(
    repo: SubcategoryRepository = Depends(get_subcategory_repository),
) -> SubcategoryService:
    """Factory for SubcategoryService."""
    return SubcategoryService(repo)


# Holiday Management Factories
def get_holiday_repository(db: AsyncSession = Depends(get_db)) -> HolidayRepository:
    """Factory for HolidayRepository."""
    return HolidayRepository(db)


def get_holiday_service(
    repo: HolidayRepository = Depends(get_holiday_repository),
) -> HolidayService:
    """Factory for HolidayService."""
    return HolidayService(repo)


# Messaging Rule Factories
def get_messaging_rule_repository(db: AsyncSession = Depends(get_db)) -> MessagingRuleRepository:
    """Factory for MessagingRuleRepository."""
    return MessagingRuleRepository(db)


def get_messaging_rule_service(
    repo: MessagingRuleRepository = Depends(get_messaging_rule_repository),
) -> MessagingRuleService:
    """Factory for MessagingRuleService."""
    return MessagingRuleService(repo)