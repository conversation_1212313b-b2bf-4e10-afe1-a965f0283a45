from app.repositories.subcategory_repository import SubcategoryRepository
from app.schemas.subcategory import SubcategoryCreateRequest, SubcategoryUpdateRequest
from app.models.subcategory import Subcategory
from typing import List, Optional, Union
from sqlalchemy.exc import IntegrityError
from app.core.api_standards import APIStandards
from fastapi import status
import uuid

class SubcategoryService:
    """Business logic for Subcategory management."""
    def __init__(self, repository: SubcategoryRepository):
        self.repository = repository

    async def create_subcategory(self, category_id: Union[str, uuid.UUID], obj_in: SubcategoryCreateRequest) -> Union[Subcategory, dict]:
        try:
            # Ensure category_id is set
            data = obj_in.model_dump()
            # Convert category_id to UUID if it's a string
            if isinstance(category_id, str):
                category_id = uuid.UUID(category_id)
            data["category_id"] = category_id
            # Check for duplicate name within the same category
            existing = await self.repository.get_by_name_and_category(data["name"], category_id)
            if existing:
                return APIStandards.create_error_response(
                    error_message="Subcategory name already exists in this category.",
                    error_title="Duplicate Subcategory",
                    status_code=status.HTTP_409_CONFLICT,
                    error_code=4000
                )
            # Create the subcategory model directly since we need to add category_id
            subcategory = Subcategory(**data)
            self.repository.db.add(subcategory)
            await self.repository.db.commit()
            await self.repository.db.refresh(subcategory)
            return subcategory
        except IntegrityError:
            return {
                "success": False,
                "message": {
                    "title": "Duplicate Subcategory",
                    "description": "Subcategory name already exists in this category."
                },
                "data": None,
                "error_code": 4000
            }
        except Exception as e:
            return {
                "success": False,
                "message": {
                    "title": "Internal Server Error",
                    "description": str(e)
                },
                "data": None,
                "error_code": 5000
            }

    async def get_subcategory(self, id: Union[str, uuid.UUID]) -> Union[Subcategory, dict]:
        try:
            subcategory = await self.repository.get(id)
            if not subcategory:
                return {
                    "success": False,
                    "message": {
                        "title": "Not Found",
                        "description": "Subcategory not found."
                    },
                    "data": None,
                    "error_code": 4003
                }
            return subcategory
        except Exception as e:
            return APIStandards.create_error_response(
                error_message=str(e),
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=5000
            )

    async def list_subcategories(self, category_id: Optional[Union[str, uuid.UUID]] = None, skip: int = 0, limit: int = 100, search: Optional[str] = None, is_active: Optional[bool] = None) -> Union[List[Subcategory], dict]:
        try:
            if search:
                # Use custom search query for text search
                from sqlalchemy import select, or_
                query = select(Subcategory).where(
                    or_(
                        Subcategory.name.ilike(f"%{search}%"),
                        Subcategory.description.ilike(f"%{search}%")
                    )
                )
                # Add category filter if specified
                if category_id is not None:
                    query = query.where(Subcategory.category_id == category_id)
                # Add active filter if specified
                if is_active is not None:
                    query = query.where(Subcategory.is_active == is_active)

                query = query.offset(skip).limit(limit)
                result = await self.repository.db.execute(query)
                return result.scalars().all()
            else:
                # Use standard get_multi for non-search queries
                filters = {}
                if category_id is not None:
                    filters["category_id"] = category_id
                if is_active is not None:
                    filters["is_active"] = is_active
                return await self.repository.get_multi(skip=skip, limit=limit, filters=filters)
        except Exception as e:
            return APIStandards.create_error_response(
                error_message=str(e),
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=5000
            )

    async def list_subcategories_by_category(self, category_id: Union[str, uuid.UUID], skip: int = 0, limit: int = 100) -> Union[List[Subcategory], dict]:
        """Get subcategories for a specific category"""
        try:
            subcategories = await self.repository.get_by_category(category_id)
            # Apply pagination
            return subcategories[skip:skip + limit]
        except Exception as e:
            return {
                "success": False,
                "message": {
                    "title": "Internal Server Error",
                    "description": str(e)
                },
                "data": None,
                "error_code": 5000
            }

    async def update_subcategory(self, id: Union[str, uuid.UUID], obj_in: SubcategoryUpdateRequest) -> Union[Subcategory, dict]:
        try:
            db_obj = await self.repository.get(id)
            if not db_obj:
                return {
                    "success": False,
                    "message": {
                        "title": "Not Found",
                        "description": "Subcategory not found."
                    },
                    "data": None,
                    "error_code": 4003
                }
            return await self.repository.update(db_obj, obj_in)
        except IntegrityError:
            return {
                "success": False,
                "message": {
                    "title": "Duplicate Subcategory",
                    "description": "Subcategory name already exists in this category."
                },
                "data": None,
                "error_code": 4000
            }
        except Exception as e:
            return {
                "success": False,
                "message": {
                    "title": "Internal Server Error",
                    "description": str(e)
                },
                "data": None,
                "error_code": 5000
            }