"""
S3 service for file upload and management
"""

import boto3
import uuid
import os
from typing import Optional
from fastapi import UploadFile, HTTPException
from botocore.exceptions import ClientError, NoCredentialsError
from datetime import datetime

from app.core.logging import logger


class S3Service:
    """Service class for S3 operations"""
    
    def __init__(self):
        # Check if AWS credentials are available
        self.aws_credentials_available = self._check_aws_credentials()
        
        if not self.aws_credentials_available:
            raise HTTPException(
                status_code=500, 
                detail="AWS S3 credentials not configured. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables."
            )
        
        try:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
                aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
                region_name=os.getenv('AWS_REGION', 'ap-south-1')
            )
            self.bucket_name = os.getenv('S3_BUCKET_NAME', 'openxcell-development-public')
            self.base_url = os.getenv('S3_BASE_URL', 'https://openxcell-development-public.s3.ap-south-1.amazonaws.com')
            self.upload_folder = 'growthhive'
            self.brochure_folder = 'brochure'
            logger.info("S3 service initialized with AWS credentials")
        except Exception as e:
            logger.error(f"Failed to initialize S3 client: {e}")
            raise HTTPException(
                status_code=500, 
                detail="Failed to initialize AWS S3 client. Please check your AWS credentials and configuration."
            )
    
    def _check_aws_credentials(self) -> bool:
        """Check if AWS credentials are available"""
        aws_access_key = os.getenv('AWS_ACCESS_KEY_ID')
        aws_secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
        
        if not aws_access_key or not aws_secret_key:
            logger.error("AWS credentials not found in environment variables")
            return False
        
        # Check if credentials are not placeholder values
        if aws_access_key in ['your-aws-access-key-id', ''] or aws_secret_key in ['your-aws-secret-access-key', '']:
            logger.error("AWS credentials are placeholder values")
            return False
        
        return True
    
    def _generate_file_name(self, original_filename: str, prefix: str = "brochure") -> str:
        """Generate a unique file name with proper folder structure"""
        file_extension = original_filename.split('.')[-1] if '.' in original_filename else 'pdf'
        unique_id = uuid.uuid4().hex[:12]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        # Use the specific folder structure: growthhive/brochure/
        return f"{self.upload_folder}/{self.brochure_folder}/{timestamp}_{unique_id}.{file_extension}"
    
    def get_full_s3_url(self, filename: str) -> str:
        """
        Construct full S3 URL from filename
        
        Args:
            filename: The filename stored in the database (e.g., "growthhive/brochure/20250622_160921_30e53058f970.pdf")
            
        Returns:
            Full S3 URL for the file
        """
        if not filename:
            return None
        
        # If filename already contains the full path, return as is
        if filename.startswith('http'):
            return filename
        
        # Construct the full URL
        return f"{self.base_url}/{filename}"
    
    def get_filename_from_url(self, url: str) -> str:
        """
        Extract filename from full S3 URL
        
        Args:
            url: Full S3 URL
            
        Returns:
            Filename (e.g., "growthhive/brochure/20250622_160921_30e53058f970.pdf")
        """
        if not url:
            return None
        
        # Remove base URL to get the filename
        if url.startswith(self.base_url):
            return url.replace(f"{self.base_url}/", "")
        
        return url
    
    async def upload_file(self, file: UploadFile, prefix: str = "brochure") -> str:
        """Upload file to S3 and return the filename (not full URL)"""
        return await self._upload_to_s3(file, prefix)
    
    async def _upload_to_s3(self, file: UploadFile, prefix: str = "brochure") -> str:
        """Upload file to S3 and return the filename"""
        try:
            # Validate file
            if not file.filename:
                raise HTTPException(status_code=400, detail="Invalid file")
            
            # Check file size (10MB limit)
            max_size = 10 * 1024 * 1024  # 10MB
            file_content = await file.read()
            if len(file_content) > max_size:
                raise HTTPException(status_code=400, detail="File size exceeds 10MB limit")
            
            # Check file type
            allowed_extensions = {'.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png'}
            file_extension = os.path.splitext(file.filename)[1].lower()
            if file_extension not in allowed_extensions:
                raise HTTPException(
                    status_code=400, 
                    detail=f"File type not allowed. Allowed types: {', '.join(allowed_extensions)}"
                )
            
            # Generate unique file name with proper folder structure
            file_name = self._generate_file_name(file.filename, prefix)
            
            # Upload to S3
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=file_name,
                Body=file_content,
                ContentType=file.content_type,
                ACL='public-read'  # Make files publicly readable
            )
            
            # Return only the filename (not the full URL)
            logger.info(f"File uploaded successfully to S3: {file_name}")
            return file_name
            
        except ClientError as e:
            logger.error(f"S3 upload error: {e}")
            raise HTTPException(status_code=500, detail="Failed to upload file to S3")
        except NoCredentialsError:
            logger.error("AWS credentials not found")
            raise HTTPException(status_code=500, detail="AWS credentials not configured")
        except Exception as e:
            logger.error(f"Unexpected error during file upload: {e}")
            raise HTTPException(status_code=500, detail="Failed to upload file")
    
    async def delete_file(self, file_name: str) -> bool:
        """Delete file from S3"""
        return await self._delete_from_s3(file_name)
    
    async def _delete_from_s3(self, file_name: str) -> bool:
        """Delete file from S3"""
        try:
            # Extract key from URL if full URL is provided
            if file_name.startswith('http'):
                file_name = self.get_filename_from_url(file_name)
            
            self.s3_client.delete_object(
                Bucket=self.bucket_name,
                Key=file_name
            )
            
            logger.info(f"File deleted successfully from S3: {file_name}")
            return True
            
        except ClientError as e:
            logger.error(f"S3 delete error: {e}")
            return False
        except NoCredentialsError:
            logger.error("AWS credentials not found")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during file deletion: {e}")
            return False
    
    async def generate_presigned_url(self, file_name: str, expiry: int = 3600) -> Optional[str]:
        """Generate presigned URL for file download"""
        try:
            # Extract key from URL if full URL is provided
            if file_name.startswith('http'):
                file_name = self.get_filename_from_url(file_name)
            
            presigned_url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': self.bucket_name,
                    'Key': file_name
                },
                ExpiresIn=expiry
            )
            
            logger.info(f"Generated presigned URL for: {file_name}")
            return presigned_url
            
        except ClientError as e:
            logger.error(f"S3 presigned URL error: {e}")
            return None
        except NoCredentialsError:
            logger.error("AWS credentials not found")
            return None
        except Exception as e:
            logger.error(f"Unexpected error generating presigned URL: {e}")
            return None
    
    async def file_exists(self, file_name: str) -> bool:
        """Check if file exists in S3"""
        return await self._s3_file_exists(file_name)
    
    async def _s3_file_exists(self, file_name: str) -> bool:
        """Check if file exists in S3"""
        try:
            # Extract key from URL if full URL is provided
            if file_name.startswith('http'):
                file_name = self.get_filename_from_url(file_name)
            
            self.s3_client.head_object(
                Bucket=self.bucket_name,
                Key=file_name
            )
            return True
            
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            logger.error(f"S3 head object error: {e}")
            return False
        except NoCredentialsError:
            logger.error("AWS credentials not found")
            return False
        except Exception as e:
            logger.error(f"Unexpected error checking file existence: {e}")
            return False
    
    async def get_file_info(self, file_name: str) -> Optional[dict]:
        """Get file information from S3"""
        return await self._get_s3_file_info(file_name)
    
    async def _get_s3_file_info(self, file_name: str) -> Optional[dict]:
        """Get file information from S3"""
        try:
            # Extract key from URL if full URL is provided
            if file_name.startswith('http'):
                file_name = self.get_filename_from_url(file_name)
            
            response = self.s3_client.head_object(
                Bucket=self.bucket_name,
                Key=file_name
            )
            
            return {
                'size': response['ContentLength'],
                'content_type': response['ContentType'],
                'last_modified': response['LastModified'],
                'etag': response['ETag']
            }
            
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return None
            logger.error(f"S3 head object error: {e}")
            return None
        except NoCredentialsError:
            logger.error("AWS credentials not found")
            return None
        except Exception as e:
            logger.error(f"Unexpected error getting file info: {e}")
            return None 