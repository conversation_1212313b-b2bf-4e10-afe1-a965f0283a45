"""Document service for business logic"""
from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_
from sqlalchemy.orm import Session
import logging
import os
from datetime import datetime

from app.models.document import Document
from app.schemas.document import (
    DocumentCreateRequest, 
    DocumentSearchRequest,
    DocumentSearchResponse,
    DocumentUploadResponse,
    DocumentProcessingStatus,
    DocumentUpdateRequest
)

logger = logging.getLogger(__name__)


class DocumentService:
    """Service for document-related business logic"""
    
    def __init__(self, db: Session):
        """Initialize document service"""
        self.db = db
    
    def create_document(self, document: DocumentCreateRequest):
        document_dict = document.dict()
        db_document = Document(**document_dict)
        self.db.add(db_document)
        self.db.commit()
        self.db.refresh(db_document)
        return db_document

    def get_document(self, document_id: str):
        return self.db.query(Document).filter(Document.id == document_id).first()

    async def get_documents(
        self,
        user_id: Optional[str] = None,
        document_type: Optional[str] = None,
        franchisor_id: Optional[str] = None,
        page: int = 1,
        limit: int = 20,
        search: Optional[str] = None
    ) -> List[Document]:
        """
        Get documents with optional filtering
        
        Args:
            user_id: Filter by user ID
            document_type: Filter by document type
            franchisor_id: Filter by franchisor ID
            page: Page number for pagination
            limit: Number of items per page
            search: Search term for document name or description
        """
        try:
            query = select(Document)
            conditions = []
            
            if user_id:
                conditions.append(Document.user_id == user_id)
            
            if document_type:
                conditions.append(Document.document_type == document_type)
            
            if franchisor_id:
                conditions.append(Document.franchisor_id == franchisor_id)
            
            if search:
                search_condition = or_(
                    Document.name.ilike(f"%{search}%"),
                    Document.description.ilike(f"%{search}%")
                )
                conditions.append(search_condition)
            
            if conditions:
                query = query.where(and_(*conditions))
            
            # Apply pagination
            offset = (page - 1) * limit
            query = query.offset(offset).limit(limit)
            
            result = await self.db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Error retrieving documents: {e}")
            raise

    def update_document(self, document_id: str, document: DocumentUpdateRequest):
        db_document = self.db.query(Document).filter(Document.id == document_id).first()
        if db_document:
            for key, value in document.dict(exclude_unset=True).items():
                setattr(db_document, key, value)
            self.db.commit()
            self.db.refresh(db_document)
        return db_document

    def delete_document(self, document_id: str):
        db_document = self.db.query(Document).filter(Document.id == document_id).first()
        if db_document:
            self.db.delete(db_document)
            self.db.commit()
        return db_document
    
    async def upload_document(
        self, 
        file_data: bytes,
        filename: str,
        document_data: DocumentCreateRequest
    ) -> DocumentUploadResponse:
        """
        Upload document file
        
        Args:
            file_data: File data
            filename: Original filename
            document_data: Document metadata
            
        Returns:
            DocumentUploadResponse: Upload result
        """
        try:
            # For now, return a mock response
            # In a real implementation, you'd save to storage (S3, local filesystem, etc.)
            
            upload_dir = "uploads/documents"
            os.makedirs(upload_dir, exist_ok=True)
            
            # Generate unique filename
            import uuid
            file_extension = os.path.splitext(filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            file_path = os.path.join(upload_dir, unique_filename)
            
            # Save file
            with open(file_path, "wb") as f:
                f.write(file_data)
            
            logger.info(f"Uploaded document file: {filename} -> {file_path}")
            
            return DocumentUploadResponse(
                document_id=str(uuid.uuid4()),
                upload_url=file_path,
                status="uploaded",
                message="Document uploaded successfully"
            )
            
        except Exception as e:
            logger.error(f"Failed to upload document {filename}: {e}")
            return DocumentUploadResponse(
                document_id="",
                upload_url="",
                status="failed",
                message=f"Upload failed: {str(e)}"
            )
    
    async def search_documents(
        self, 
        search_request: DocumentSearchRequest
    ) -> DocumentSearchResponse:
        """
        Search documents using RAG
        
        Args:
            search_request: Search request parameters
            
        Returns:
            DocumentSearchResponse: Search results
        """
        try:
            # For now, return mock results
            # In a real implementation, you'd use vector search with embeddings
            
            from time import time
            start_time = time()
            
            # Mock search results
            results = []
            
            search_time = time() - start_time
            
            logger.info(f"Document search completed for query: {search_request.query}")
            
            return DocumentSearchResponse(
                query=search_request.query,
                results=results,
                total_results=len(results),
                search_time=search_time
            )
            
        except Exception as e:
            logger.error(f"Failed to search documents: {e}")
            raise
    
    async def get_processing_status(
        self, 
        document_id: str
    ) -> DocumentProcessingStatus:
        """
        Get document processing status
        
        Args:
            document_id: Document ID
            
        Returns:
            DocumentProcessingStatus: Processing status
        """
        try:
            # For now, return mock status
            # In a real implementation, you'd check actual processing status
            
            return DocumentProcessingStatus(
                document_id=document_id,
                status="processed",
                progress=100,
                message="Document processing completed",
                started_at=datetime.utcnow(),
                completed_at=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"Failed to get processing status for document {document_id}: {e}")
            raise
    
    async def extract_text(
        self, 
        document_id: str
    ) -> str:
        """
        Extract text from document
        
        Args:
            document_id: Document ID
            
        Returns:
            str: Extracted text
        """
        try:
            # For now, return mock text
            # In a real implementation, you'd use document processing libraries
            
            return "Mock extracted text from document"
            
        except Exception as e:
            logger.error(f"Failed to extract text from document {document_id}: {e}")
            raise
    
    async def count_documents(
        self, 
        db: AsyncSession,
        franchisor_id: Optional[str] = None,
        document_type: Optional[str] = None,
        status: Optional[str] = None
    ) -> int:
        """
        Count documents with filtering
        
        Args:
            db: Database session
            franchisor_id: Filter by franchisor ID
            document_type: Filter by document type
            status: Filter by status
            
        Returns:
            int: Number of documents
        """
        try:
            from sqlalchemy import func
            
            query = select(func.count(Document.id))
            
            # Apply filters
            conditions = []
            
            if franchisor_id:
                conditions.append(Document.franchisor_id == franchisor_id)
            
            if document_type:
                conditions.append(Document.document_type == document_type)
            
            if status:
                conditions.append(Document.status == status)
            
            if conditions:
                query = query.where(and_(*conditions))
            
            result = await db.execute(query)
            return result.scalar()
            
        except Exception as e:
            logger.error(f"Failed to count documents: {e}")
            raise

    async def get_documents_by_franchisor(
        self,
        franchisor_id: str,
        page: int = 1,
        limit: int = 20
    ) -> List[Document]:
        """
        Get documents by franchisor ID
        
        Args:
            franchisor_id: Franchisor ID to filter by
            page: Page number for pagination
            limit: Number of items per page
        """
        try:
            query = select(Document).where(Document.franchisor_id == franchisor_id)
            
            # Apply pagination
            offset = (page - 1) * limit
            query = query.offset(offset).limit(limit)
            
            result = await self.db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Error retrieving documents by franchisor: {e}")
            raise
