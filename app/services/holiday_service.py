"""
Holiday Service
Business logic layer for holiday management
"""

from datetime import date
from typing import List, Optional, Union, Dict, Any
from uuid import UUID
from fastapi import status
from app.models.holiday import Holiday
from app.schemas.holiday import (
    HolidayCreateRequest,
    HolidayUpdateRequest,
    HolidayResponse,
)
from app.repositories.holiday_repository import HolidayRepository
from app.core.api_standards import APIStandards
from app.core.logging import logger


class HolidayService:
    """Service for holiday management operations"""

    def __init__(self, repository: HolidayRepository):
        self.repository = repository

    async def create_holiday(
        self, obj_in: HolidayCreateRequest
    ) -> Union[Holiday, dict]:
        """
        Create a new holiday

        Args:
            obj_in: Holiday creation data

        Returns:
            Created holiday instance or error response
        """
        try:
            # Check for duplicate holiday on the same date and type
            existing_holidays = await self.repository.get_holidays_by_date_range(
                start_date=obj_in.date,
                end_date=obj_in.date,
                holiday_type=obj_in.holiday_type,
            )

            if existing_holidays:
                return APIStandards.create_error_response(
                    error_message=f"A {obj_in.holiday_type.lower()} holiday already exists on {obj_in.date}",
                    error_title="Duplicate Holiday",
                    status_code=status.HTTP_409_CONFLICT,
                    error_code=4001,
                )

            # Create the holiday
            result = await self.repository.create(obj_in)

            if not isinstance(result, Holiday):
                return APIStandards.create_error_response(
                    error_message="Failed to create holiday",
                    error_title="Creation Failed",
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    error_code=5001,
                )

            logger.info(f"Holiday created successfully: {result.id}")
            return result

        except Exception as e:
            logger.error(f"Error creating holiday: {str(e)}", exc_info=True)
            return APIStandards.create_error_response(
                error_message="An error occurred while creating the holiday",
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=5001,
            )

    async def get_holiday(self, holiday_id: str) -> Union[Holiday, dict]:
        """
        Get a holiday by ID

        Args:
            holiday_id: Holiday UUID

        Returns:
            Holiday instance or error response
        """
        try:
            holiday_uuid = UUID(holiday_id)
            holiday = await self.repository.get(holiday_uuid)

            if not holiday or holiday.is_deleted:
                return APIStandards.create_error_response(
                    error_message="Holiday not found",
                    error_title="Not Found",
                    status_code=status.HTTP_404_NOT_FOUND,
                    error_code=4004,
                )

            return holiday

        except ValueError:
            return APIStandards.create_error_response(
                error_message="Invalid holiday ID format",
                error_title="Invalid ID",
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code=4002,
            )
        except Exception as e:
            logger.error(
                f"Error retrieving holiday {holiday_id}: {str(e)}", exc_info=True
            )
            return APIStandards.create_error_response(
                error_message="An error occurred while retrieving the holiday",
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=5002,
            )

    async def update_holiday(
        self, holiday_id: str, obj_in: HolidayUpdateRequest
    ) -> Union[Holiday, dict]:
        """
        Update a holiday

        Args:
            holiday_id: Holiday UUID
            obj_in: Holiday update data

        Returns:
            Updated holiday instance or error response
        """
        try:
            holiday_uuid = UUID(holiday_id)
            existing_holiday = await self.repository.get(holiday_uuid)

            if not existing_holiday or existing_holiday.is_deleted:
                return APIStandards.create_error_response(
                    error_message="Holiday not found",
                    error_title="Not Found",
                    status_code=status.HTTP_404_NOT_FOUND,
                    error_code=4004,
                )

            # Check for duplicate if date or type is being changed
            if obj_in.date or obj_in.holiday_type:
                check_date = obj_in.date if obj_in.date else existing_holiday.date
                check_type = (
                    obj_in.holiday_type
                    if obj_in.holiday_type
                    else existing_holiday.holiday_type
                )

                # Only check for duplicates if the date or type is actually changing
                if (
                    check_date != existing_holiday.date
                    or check_type != existing_holiday.holiday_type
                ):
                    existing_holidays = (
                        await self.repository.get_holidays_by_date_range(
                            start_date=check_date,
                            end_date=check_date,
                            holiday_type=check_type,
                        )
                    )

                    # Filter out the current holiday from the check
                    duplicate_holidays = [
                        h for h in existing_holidays if h.id != holiday_uuid
                    ]

                    if duplicate_holidays:
                        return APIStandards.create_error_response(
                            error_message=f"A {check_type.lower()} holiday already exists on {check_date}",
                            error_title="Duplicate Holiday",
                            status_code=status.HTTP_409_CONFLICT,
                            error_code=4001,
                        )

            # Update the holiday
            result = await self.repository.update(holiday_uuid, obj_in)

            if not isinstance(result, Holiday):
                return APIStandards.create_error_response(
                    error_message="Failed to update holiday",
                    error_title="Update Failed",
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    error_code=5003,
                )

            logger.info(f"Holiday updated successfully: {holiday_id}")
            return result

        except ValueError:
            return APIStandards.create_error_response(
                error_message="Invalid holiday ID format",
                error_title="Invalid ID",
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code=4002,
            )
        except Exception as e:
            logger.error(
                f"Error updating holiday {holiday_id}: {str(e)}", exc_info=True
            )
            return APIStandards.create_error_response(
                error_message="An error occurred while updating the holiday",
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=5003,
            )

    async def delete_holiday(self, holiday_id: str) -> Union[bool, dict]:
        """
        Soft delete a holiday

        Args:
            holiday_id: Holiday UUID

        Returns:
            True if successful or error response
        """
        try:
            holiday_uuid = UUID(holiday_id)
            result = await self.repository.soft_delete(holiday_uuid)

            if not result:
                return APIStandards.create_error_response(
                    error_message="Holiday not found",
                    error_title="Not Found",
                    status_code=status.HTTP_404_NOT_FOUND,
                    error_code=4004,
                )

            logger.info(f"Holiday deleted successfully: {holiday_id}")
            return True

        except ValueError:
            return APIStandards.create_error_response(
                error_message="Invalid holiday ID format",
                error_title="Invalid ID",
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code=4002,
            )
        except Exception as e:
            logger.error(
                f"Error deleting holiday {holiday_id}: {str(e)}", exc_info=True
            )
            return APIStandards.create_error_response(
                error_message="An error occurred while deleting the holiday",
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=5004,
            )

    async def list_holidays(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        holiday_type: Optional[str] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
    ) -> Union[List[Holiday], dict]:
        """
        List holidays with optional filtering

        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            search: Optional search term for description
            holiday_type: Optional filter by holiday type
            start_date: Optional start date filter
            end_date: Optional end date filter

        Returns:
            List of holidays or error response
        """
        try:
            if search:
                return await self.repository.search_holidays(search, skip, limit)
            elif start_date and end_date:
                return await self.repository.get_holidays_by_date_range(
                    start_date, end_date, holiday_type, skip, limit
                )
            elif holiday_type:
                return await self.repository.get_holidays_by_type(
                    holiday_type, skip, limit
                )
            else:
                return await self.repository.get_active_holidays(skip, limit)

        except Exception as e:
            logger.error(f"Error listing holidays: {str(e)}", exc_info=True)
            return APIStandards.create_error_response(
                error_message="An error occurred while retrieving holidays",
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=5005,
            )

    async def count_holidays(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        Count holidays with optional filtering

        Args:
            filters: Optional filters to apply

        Returns:
            Number of holidays
        """
        try:
            return await self.repository.count_active_holidays(filters)
        except Exception as e:
            logger.error(f"Error counting holidays: {str(e)}", exc_info=True)
            return 0
