"""
Local file storage service for development
"""

import os
import uuid
from typing import Optional
from fastapi import UploadFile, HTTPException
from datetime import datetime
from pathlib import Path

from app.core.logging import logger


class LocalFileService:
    """Local file storage service for development"""
    
    def __init__(self):
        self.upload_dir = Path("uploads")
        self.upload_dir.mkdir(exist_ok=True)
        self.base_url = "http://localhost:8000/uploads"
    
    def _generate_file_name(self, original_filename: str, prefix: str = "brochures") -> str:
        """Generate a unique file name"""
        file_extension = original_filename.split('.')[-1] if '.' in original_filename else 'pdf'
        unique_id = uuid.uuid4().hex[:12]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{prefix}/{timestamp}_{unique_id}.{file_extension}"
    
    async def upload_file(self, file: UploadFile, prefix: str = "brochures") -> str:
        """Upload file to local storage and return the URL"""
        try:
            # Validate file
            if not file.filename:
                raise HTTPException(status_code=400, detail="Invalid file")
            
            # Check file size (10MB limit)
            max_size = 10 * 1024 * 1024  # 10MB
            file_content = await file.read()
            if len(file_content) > max_size:
                raise HTTPException(status_code=400, detail="File size exceeds 10MB limit")
            
            # Check file type
            allowed_extensions = {'.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png'}
            file_extension = os.path.splitext(file.filename)[1].lower()
            if file_extension not in allowed_extensions:
                raise HTTPException(
                    status_code=400, 
                    detail=f"File type not allowed. Allowed types: {', '.join(allowed_extensions)}"
                )
            
            # Generate unique file name
            file_name = self._generate_file_name(file.filename, prefix)
            file_path = self.upload_dir / file_name
            
            # Create directory if it doesn't exist
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save file
            with open(file_path, "wb") as f:
                f.write(file_content)
            
            # Return the file URL
            file_url = f"{self.base_url}/{file_name}"
            logger.info(f"File uploaded successfully to local storage: {file_url}")
            return file_url
            
        except Exception as e:
            logger.error(f"Local file upload error: {e}")
            raise HTTPException(status_code=500, detail="Failed to upload file")
    
    async def delete_file(self, file_name: str) -> bool:
        """Delete file from local storage"""
        try:
            # Extract path from URL if full URL is provided
            if file_name.startswith(self.base_url):
                file_name = file_name.replace(f"{self.base_url}/", "")
            
            file_path = self.upload_dir / file_name
            
            if file_path.exists():
                file_path.unlink()
                logger.info(f"File deleted successfully from local storage: {file_name}")
                return True
            else:
                logger.warning(f"File not found in local storage: {file_name}")
                return False
            
        except Exception as e:
            logger.error(f"Local file deletion error: {e}")
            return False
    
    async def file_exists(self, file_name: str) -> bool:
        """Check if file exists in local storage"""
        try:
            # Extract path from URL if full URL is provided
            if file_name.startswith(self.base_url):
                file_name = file_name.replace(f"{self.base_url}/", "")
            
            file_path = self.upload_dir / file_name
            return file_path.exists()
            
        except Exception as e:
            logger.error(f"Local file existence check error: {e}")
            return False
    
    async def get_file_info(self, file_name: str) -> Optional[dict]:
        """Get file information from local storage"""
        try:
            # Extract path from URL if full URL is provided
            if file_name.startswith(self.base_url):
                file_name = file_name.replace(f"{self.base_url}/", "")
            
            file_path = self.upload_dir / file_name
            
            if not file_path.exists():
                return None
            
            stat = file_path.stat()
            return {
                'size': stat.st_size,
                'content_type': 'application/octet-stream',  # Default
                'last_modified': datetime.fromtimestamp(stat.st_mtime),
                'etag': str(stat.st_mtime)
            }
            
        except Exception as e:
            logger.error(f"Local file info error: {e}")
            return None 