"""
Franchisor service for business logic operations
"""

import uuid
from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_, func
from datetime import datetime
from fastapi import HTTPException

from app.models.franchisor import Franchisor
from app.models.category import Category
from app.models.subcategory import Subcategory
from app.schemas.franchisor import (
    FranchisorCreateRequest,
    FranchisorUpdateRequest,
    FranchisorResponse,
    FranchisorRegion
)
from app.schemas.category import CategoryResponse
from app.schemas.subcategory import SubcategoryResponse
from app.core.logging import logger


class FranchisorService:
    """Service class for franchisor operations"""
    
    def __init__(self, db: AsyncSession):
        """Initialize franchisor service"""
        self.db = db
        try:
            from app.services.s3_service import S3Service
            self.s3_service = S3Service()
        except Exception as e:
            logger.warning(f"S3 service initialization failed: {e}")
            self.s3_service = None
    
    def _generate_franchisor_id(self) -> str:
        """Generate a unique franchisor ID"""
        return uuid.uuid4()

    def _convert_region_to_enum(self, region_value: str) -> Optional[str]:
        """Convert database region value to enum value"""
        if not region_value:
            return None

        # Mapping from database values to enum values
        region_mapping = {
            'North America': 'north_america',
            'Europe': 'europe',
            'Asia Pacific': 'asia_pacific',
            'Latin America': 'latin_america',
            'Middle East': 'middle_east',
            'Africa': 'africa',
            'Australia': 'australia',
            'Global': 'global',
            # Also handle cases where the value is already in enum format
            'north_america': 'north_america',
            'europe': 'europe',
            'asia_pacific': 'asia_pacific',
            'latin_america': 'latin_america',
            'middle_east': 'middle_east',
            'africa': 'africa',
            'australia': 'australia',
            'global': 'global'
        }

        return region_mapping.get(region_value, region_value.lower().replace(' ', '_'))

    async def _get_full_brochure_url(self, filename: str) -> Optional[str]:
        """Get full S3 URL for brochure filename"""
        if not filename:
            return None

        # If S3 service is not available, return the filename as is
        if not self.s3_service:
            logger.warning("S3 service not available, returning filename as is")
            return filename

        return self.s3_service.get_full_s3_url(filename)

    async def _validate_category_relationship(self, category_id: Optional[str], subcategory_id: Optional[str]) -> bool:
        """Validate that subcategory belongs to the specified category using UUIDs"""
        if not category_id or not subcategory_id:
            return True  # No validation needed if either is None

        # Check if subcategory belongs to the category
        query = select(Subcategory).where(
            Subcategory.id == subcategory_id,
            Subcategory.category_id == category_id,
            Subcategory.is_active == True,
            Subcategory.is_deleted == False
        )
        result = await self.db.execute(query)
        subcategory = result.scalar_one_or_none()
        return subcategory is not None

    async def _get_category_details(self, category_id: Optional[str]) -> Optional[CategoryResponse]:
        """Get category details by UUID"""
        if not category_id:
            return None

        query = select(Category).where(
            Category.id == category_id,
            Category.is_active == True,
            Category.is_deleted == False
        )
        result = await self.db.execute(query)
        category = result.scalar_one_or_none()

        if category:
            return CategoryResponse(
                id=str(category.id),
                name=category.name,
                description=category.description,
                is_active=category.is_active,
                is_deleted=category.is_deleted,
                created_at=category.created_at,
                updated_at=category.updated_at
            )
        return None

    async def _get_subcategory_details(self, subcategory_id: Optional[str]) -> Optional[SubcategoryResponse]:
        """Get subcategory details by UUID"""
        if not subcategory_id:
            return None

        query = select(Subcategory).where(
            Subcategory.id == subcategory_id,
            Subcategory.is_active == True,
            Subcategory.is_deleted == False
        )
        result = await self.db.execute(query)
        subcategory = result.scalar_one_or_none()

        if subcategory:
            return SubcategoryResponse(
                id=str(subcategory.id),
                name=subcategory.name,
                description=subcategory.description,
                is_active=subcategory.is_active,
                is_deleted=subcategory.is_deleted,
                category_id=str(subcategory.category_id),
                created_at=subcategory.created_at,
                updated_at=subcategory.updated_at
            )
        return None
    
    async def create_franchisor(self, franchisor_data: FranchisorCreateRequest) -> Franchisor:
        """Create a new franchisor with category/subcategory relationship validation"""
        try:
            # Get category and subcategory IDs
            category_id = franchisor_data.category_id
            subcategory_id = franchisor_data.subcategory_id

            # Validate category/subcategory relationship if both are provided
            if category_id and subcategory_id:
                is_valid = await self._validate_category_relationship(category_id, subcategory_id)
                if not is_valid:
                    raise HTTPException(
                        status_code=400,
                        detail="Subcategory does not belong to the specified category"
                    )

            franchisor = Franchisor(
                id=self._generate_franchisor_id(),
                name=franchisor_data.name,
                category_id=category_id,
                subcategory_id=subcategory_id,
                region=franchisor_data.region.value if franchisor_data.region else None,
                budget=franchisor_data.budget,
                is_active=franchisor_data.is_active
            )

            self.db.add(franchisor)
            await self.db.commit()
            await self.db.refresh(franchisor)

            logger.info(f"Created franchisor: {franchisor.id}")
            return franchisor

        except HTTPException:
            await self.db.rollback()
            raise
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating franchisor: {e}")
            raise
    
    async def get_franchisor_by_id(self, franchisor_id: str) -> Optional[FranchisorResponse]:
        """Get franchisor by ID with category/subcategory details"""
        try:
            query = select(Franchisor).where(Franchisor.id == franchisor_id)
            result = await self.db.execute(query)
            franchisor = result.scalar_one_or_none()

            if not franchisor:
                return None

            # Get category and subcategory details
            category_details = await self._get_category_details(franchisor.category_id)
            subcategory_details = await self._get_subcategory_details(franchisor.subcategory_id)

            # Process brochure URL
            brochure_url = franchisor.brochure_url
            if brochure_url:
                brochure_url = await self._get_full_brochure_url(brochure_url)

            # Create response with relationship data
            return FranchisorResponse(
                id=str(franchisor.id),
                name=franchisor.name,
                category_id=str(franchisor.category_id) if franchisor.category_id else None,
                subcategory_id=str(franchisor.subcategory_id) if franchisor.subcategory_id else None,
                category_details=category_details,
                subcategory_details=subcategory_details,
                region=self._convert_region_to_enum(franchisor.region),
                budget=franchisor.budget,
                brochure_url=brochure_url,
                is_active=franchisor.is_active,
                created_at=franchisor.created_at,
                updated_at=franchisor.updated_at
            )

        except Exception as e:
            logger.error(f"Error retrieving franchisor {franchisor_id}: {e}")
            raise
    
    async def get_franchisors(
        self,
        skip: int = 0,
        limit: int = 20,
        category: Optional[str] = None,
        region: Optional[str] = None,
        is_active: Optional[bool] = None,
        search: Optional[str] = None
    ) -> Tuple[List[FranchisorResponse], int]:
        """Get franchisors with pagination and filtering"""
        try:
            # Build query
            query = select(Franchisor)
            
            # Apply filters
            filters = []
            if category:
                # Assume category is a category_id for filtering
                filters.append(Franchisor.category_id == category)
            if region:
                filters.append(Franchisor.region == region)
            if is_active is not None:
                filters.append(Franchisor.is_active == is_active)
            if search:
                # Case-insensitive search by name
                filters.append(Franchisor.name.ilike(f"%{search}%"))
            
            if filters:
                query = query.where(and_(*filters))
            
            # Get total count
            count_query = select(func.count(Franchisor.id))
            if filters:
                count_query = count_query.where(and_(*filters))
            
            total_count = await self.db.scalar(count_query)
            
            # Apply pagination and ordering
            query = query.order_by(Franchisor.created_at.desc()).offset(skip).limit(limit)
            
            # Execute query
            result = await self.db.execute(query)
            franchisors = result.scalars().all()
            
            # Convert to list and process with relationship data
            franchisor_list = []
            for franchisor in franchisors:
                # Get category and subcategory details
                category_details = await self._get_category_details(franchisor.category_id)
                subcategory_details = await self._get_subcategory_details(franchisor.subcategory_id)

                # Process brochure URL
                brochure_url = franchisor.brochure_url
                if brochure_url and self.s3_service:
                    try:
                        brochure_url = self.s3_service.get_full_s3_url(brochure_url)
                    except Exception as e:
                        logger.warning(f"Failed to get full S3 URL for {brochure_url}: {e}")
                        # Keep the original URL if S3 service fails

                # Create response with relationship data
                franchisor_response = FranchisorResponse(
                    id=str(franchisor.id),
                    name=franchisor.name,
                    category_id=str(franchisor.category_id) if franchisor.category_id else None,
                    subcategory_id=str(franchisor.subcategory_id) if franchisor.subcategory_id else None,
                    category_details=category_details,
                    subcategory_details=subcategory_details,
                    region=self._convert_region_to_enum(franchisor.region),
                    budget=franchisor.budget,
                    brochure_url=brochure_url,
                    is_active=franchisor.is_active,
                    created_at=franchisor.created_at,
                    updated_at=franchisor.updated_at
                )

                franchisor_list.append(franchisor_response)
            
            return franchisor_list, total_count
            
        except Exception as e:
            logger.error(f"Error retrieving franchisors: {e}")
            raise HTTPException(
                status_code=500,
                detail="Failed to retrieve franchisors"
            )
    
    async def count_franchisors(
        self,
        category: Optional[str] = None,
        region: Optional[str] = None,
        is_active: Optional[bool] = None,
        search: Optional[str] = None
    ) -> int:
        """Count franchisors with filters"""
        try:
            query = select(func.count(Franchisor.id))

            # Apply filters
            filters = []
            if category:
                # Assume category is a category_id for filtering
                filters.append(Franchisor.category_id == category)
            if region:
                filters.append(Franchisor.region == region)
            if is_active is not None:
                filters.append(Franchisor.is_active == is_active)
            if search:
                search_filter = or_(
                    Franchisor.name.ilike(f"%{search}%"),
                    Franchisor.region.ilike(f"%{search}%")
                )
                filters.append(search_filter)

            if filters:
                query = query.where(and_(*filters))

            result = await self.db.execute(query)
            return result.scalar()

        except Exception as e:
            logger.error(f"Error counting franchisors: {e}")
            raise
    
    async def update_franchisor(self, franchisor_id: str, franchisor_data: FranchisorUpdateRequest, brochure_file=None) -> Optional[Franchisor]:
        """Update franchisor with optional brochure upload and category/subcategory validation"""
        try:
            query = select(Franchisor).where(Franchisor.id == franchisor_id)
            result = await self.db.execute(query)
            franchisor = result.scalar_one_or_none()
            if not franchisor:
                return None

            # Validate category/subcategory relationship if both are provided
            category_id = franchisor_data.category_id if franchisor_data.category_id is not None else franchisor.category_id
            subcategory_id = franchisor_data.subcategory_id if franchisor_data.subcategory_id is not None else franchisor.subcategory_id

            if category_id and subcategory_id:
                is_valid = await self._validate_category_relationship(category_id, subcategory_id)
                if not is_valid:
                    raise HTTPException(
                        status_code=400,
                        detail="Subcategory does not belong to the specified category"
                    )

            # Update basic fields
            if franchisor_data.name is not None:
                franchisor.name = franchisor_data.name

            # Update ID-based fields
            if franchisor_data.category_id is not None:
                franchisor.category_id = franchisor_data.category_id
            if franchisor_data.subcategory_id is not None:
                franchisor.subcategory_id = franchisor_data.subcategory_id

            if franchisor_data.region is not None:
                franchisor.region = franchisor_data.region.value
            if franchisor_data.budget is not None:
                franchisor.budget = franchisor_data.budget
            if franchisor_data.is_active is not None:
                franchisor.is_active = franchisor_data.is_active
            
            # Handle brochure upload if provided
            if brochure_file and self.s3_service:
                try:
                    brochure_filename = await self.s3_service.upload_file(brochure_file, prefix="brochures")
                    franchisor.brochure_url = brochure_filename
                    logger.info(f"Uploaded brochure for franchisor {franchisor_id}: {brochure_filename}")
                except Exception as e:
                    logger.error(f"Failed to upload brochure for franchisor {franchisor_id}: {e}")
                    # Continue with update even if brochure upload fails
            
            franchisor.updated_at = datetime.utcnow()
            await self.db.commit()
            await self.db.refresh(franchisor)
            return franchisor
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating franchisor: {e}")
            raise
    
    async def delete_franchisor(self, franchisor_id: str) -> bool:
        """Delete franchisor"""
        try:
            # Check if franchisor exists
            existing_franchisor = await self.get_franchisor_by_id(franchisor_id)
            if not existing_franchisor:
                return False
            
            # Delete franchisor
            query = delete(Franchisor).where(Franchisor.id == franchisor_id)
            await self.db.execute(query)
            await self.db.commit()
            
            logger.info(f"Deleted franchisor: {franchisor_id}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error deleting franchisor {franchisor_id}: {e}")
            raise
    
    async def update_brochure_url(self, franchisor_id: str, brochure_filename: str) -> Optional[Franchisor]:
        """Update franchisor brochure filename"""
        try:
            query = update(Franchisor).where(Franchisor.id == franchisor_id).values(
                brochure_url=brochure_filename,
                updated_at=datetime.utcnow()
            )
            await self.db.execute(query)
            await self.db.commit()
            
            return await self.get_franchisor_by_id(franchisor_id)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating brochure URL for franchisor {franchisor_id}: {e}")
            raise
    
    async def bulk_create_franchisors(self, franchisors_data: List[Dict[str, Any]]) -> List[Franchisor]:
        """Bulk create franchisors"""
        try:
            franchisors = []
            for data in franchisors_data:
                franchisor = Franchisor(
                    id=self._generate_franchisor_id(),
                    name=data["name"],
                    category_id=data.get("category_id"),
                    subcategory_id=data.get("subcategory_id"),
                    region=data.get("region"),
                    budget=data.get("budget"),
                    is_active=data.get("is_active", True)
                )
                franchisors.append(franchisor)

            self.db.add_all(franchisors)
            await self.db.commit()

            # Refresh all franchisors
            for franchisor in franchisors:
                await self.db.refresh(franchisor)

            logger.info(f"Bulk created {len(franchisors)} franchisors")
            return franchisors

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error bulk creating franchisors: {e}")
            raise