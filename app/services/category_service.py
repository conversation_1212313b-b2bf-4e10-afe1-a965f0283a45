from app.repositories.category_repository import CategoryRepository
from app.schemas.category import CategoryCreateRequest, CategoryUpdateRequest
from app.models.category import Category
from typing import List, Optional, Union
from sqlalchemy.exc import IntegrityError
from app.core.api_standards import APIStandards
from fastapi import status
import uuid

class CategoryService:
    """Business logic for Category management."""
    def __init__(self, repository: CategoryRepository):
        self.repository = repository

    async def create_category(self, obj_in: CategoryCreateRequest) -> Union[Category, dict]:
        try:
            existing = await self.repository.get_by_name(obj_in.name)
            if existing:
                return APIStandards.create_error_response(
                    error_message="Category name already exists.",
                    error_title="Duplicate Category",
                    status_code=status.HTTP_409_CONFLICT,
                    error_code=4000
                )
            result = await self.repository.create(obj_in)
            # If result is not a Category instance, return a generic error
            if not isinstance(result, Category):
                return APIStandards.create_error_response(
                    error_message="Failed to create category.",
                    error_title="Creation Failed",
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    error_code=5000
                )
            return result
        except IntegrityError:
            return APIStandards.create_error_response(
                error_message="Category name already exists.",
                error_title="Duplicate Category",
                status_code=status.HTTP_409_CONFLICT,
                error_code=4000
            )
        except Exception as e:
            return APIStandards.create_error_response(
                error_message=str(e),
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=5000
            )

    async def get_category(self, id: Union[str, uuid.UUID]) -> Union[Category, dict]:
        try:
            category = await self.repository.get(id)
            if not category:
                return APIStandards.create_error_response(
                    error_message="Category not found.",
                    error_title="Not Found",
                    status_code=status.HTTP_404_NOT_FOUND
                )
            return category
        except Exception as e:
            return APIStandards.create_error_response(
                error_message=str(e),
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    async def list_categories(self, skip: int = 0, limit: int = 100, search: Optional[str] = None) -> Union[List[Category], dict]:
        try:
            if search:
                # Use custom search query for text search
                from sqlalchemy import select, or_
                query = select(Category).where(
                    or_(
                        Category.name.ilike(f"%{search}%"),
                        Category.description.ilike(f"%{search}%")
                    )
                ).offset(skip).limit(limit)
                result = await self.repository.db.execute(query)
                return result.scalars().all()
            else:
                # Use standard get_multi for non-search queries
                return await self.repository.get_multi(skip=skip, limit=limit)
        except Exception as e:
            return APIStandards.create_error_response(
                error_message=str(e),
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    async def update_category(self, id: Union[str, uuid.UUID], obj_in: CategoryUpdateRequest) -> Union[Category, dict]:
        try:
            db_obj = await self.repository.get(id)
            if not db_obj:
                return APIStandards.create_error_response(
                    error_message="Category not found.",
                    error_title="Not Found",
                    status_code=status.HTTP_404_NOT_FOUND
                )
            return await self.repository.update(db_obj, obj_in)
        except IntegrityError:
            return APIStandards.create_error_response(
                error_message="Category name already exists.",
                error_title="Duplicate Category",
                status_code=status.HTTP_409_CONFLICT
            )
        except Exception as e:
            return APIStandards.create_error_response(
                error_message=str(e),
                error_title="Internal Server Error",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )